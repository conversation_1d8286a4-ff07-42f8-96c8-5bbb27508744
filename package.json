{"name": "monorepo", "version": "0.0.1", "private": true, "packageManager": "pnpm@9.10.0", "description": "A GUI Agent application based on UI-TARS(Vision-Lanuage Model) that allows you to control your computer using natural language.", "main": "./dist/main/main.js", "author": "ByteDance", "scripts": {"commit": "oco", "bootstrap": "pnpm i", "dev:ui-tars": "turbo run ui-tars-desktop#dev", "dev:agent-tars": "turbo run agent-tars-app#dev", "format": "prettier --write .", "lint": "eslint . --ext .js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix", "test": "vitest", "test:bench": "vitest bench", "coverage": "vitest run --coverage", "publish:packages": "bash scripts/release-pkgs.sh", "publish-beta:packages": "bash scripts/release-beta-pkgs.sh", "prepare": "husky"}, "pnpm": {"patchedDependencies": {}}, "devDependencies": {"@changesets/cli": "^2.27.11", "@commitlint/cli": "^19.6.1", "@commitlint/config-conventional": "^19.6.0", "@electron-toolkit/tsconfig": "^1.0.1", "@playwright/test": "^1.49.1", "@trivago/prettier-plugin-sort-imports": "^5.2.1", "@types/node": "^20.14.8", "@typescript-eslint/eslint-plugin": "^5.0.0", "@typescript-eslint/parser": "^5.0.0", "@vitest/coverage-istanbul": "^3.0.3", "@vitest/coverage-v8": "^3.0.8", "cross-env": "^7.0.3", "electron-playwright-helpers": "^1.7.1", "eslint": "^8.57.0", "eslint-plugin-import": "^2.25.0", "eslint-plugin-react": "^7.34.3", "husky": "^9.1.7", "lint-staged": "^14.0.1", "opencommit": "^3.2.5", "prettier": "^3.3.2", "rimraf": "^6.0.1", "sass-embedded": "^1.83.1", "ts-node": "^10.9.2", "tsx": "^4.19.2", "turbo": "^2.5.4", "typescript": "^5.7.2"}, "engines": {"node": ">=20.x"}}