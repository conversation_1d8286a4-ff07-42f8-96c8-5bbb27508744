{"name": "@agent-infra/browser-search", "version": "0.0.2", "main": "dist/index.js", "module": "dist/index.mjs", "types": "dist/index.d.ts", "exports": {".": {"import": "./dist/index.mjs", "require": "./dist/index.js", "types": "./dist/index.d.ts"}}, "files": ["dist"], "scripts": {"dev": "rslib build --watch", "build": "rslib build", "prepare": "npm run build", "prepublishOnly": "pnpm run build", "test": "vitest run", "test:watch": "vitest", "test:e2e": "vitest --config vitest.e2e.config.ts", "coverage": "vitest run --coverage", "benchmark": "ts-node examples/benchmark.ts"}, "dependencies": {"@agent-infra/browser": "workspace:*", "@agent-infra/logger": "workspace:*", "@agent-infra/shared": "workspace:*"}, "devDependencies": {"@types/node": "20.14.8", "rimraf": "4.1.0", "typescript": "4.9.4", "vitest": "3.0.7", "@vitest/coverage-v8": "3.0.7", "ts-node": "10.9.2", "@rslib/core": "0.5.3"}}