{"name": "@agent-infra/shared", "version": "0.0.1", "main": "dist/index.js", "module": "dist/index.mjs", "types": "dist/index.d.ts", "exports": {".": {"import": "./dist/index.mjs", "require": "./dist/index.js", "types": "./dist/index.d.ts"}}, "files": ["dist"], "scripts": {"dev": "rslib build --watch", "build": "rslib build", "build:readability": "ts-node scripts/build-readability.ts", "prepare": "npm run build", "prepublishOnly": "pnpm run build", "test": "vitest run", "test:watch": "vitest"}, "dependencies": {"turndown": "^7.2.0", "@types/turndown": "^5.0.5", "turndown-plugin-gfm": "^1.0.2"}, "devDependencies": {"@agent-infra/mcp-shared": "workspace:*", "@mozilla/readability": "^0.5.0", "@types/node": "20.14.8", "typescript": "4.9.4", "vitest": "3.0.7", "@rslib/core": "0.5.3", "ts-node": "10.9.2"}}