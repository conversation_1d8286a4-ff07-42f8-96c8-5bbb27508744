# UI-TARS Ollama集成补丁包

## 📦 快速使用方案

由于构建环境的复杂性，我们建议采用以下方案：

### 方案A: 下载预构建版本（推荐）

1. **下载最新版本**：
   - 访问：https://github.com/bytedance/UI-TARS-desktop/releases
   - 下载：`Agent-TARS-v1.0.0-alpha.9` Windows版本
   - 文件：`Agent-TARS-1.0.0-alpha.9.Setup.exe`

2. **安装并配置Ollama**：
   - 安装应用后，进入设置
   - 配置如下：
     * Provider: "Ollama"
     * Model: "qwen2.5vl:3b"
     * API Key: "ollama"
     * Endpoint: "http://localhost:11434/v1"

### 方案B: 源码集成（如果您想自己构建）

#### 必要文件列表

1. **后端Provider**：
   - `apps/agent-tars/src/main/llmProvider/providers/OllamaProvider.ts`

2. **Provider工厂**：
   - `apps/agent-tars/src/main/llmProvider/ProviderFactory.ts`

3. **类型定义**：
   - `packages/agent-infra/shared/src/agent-tars-types/setting.ts`

4. **前端UI**：
   - `apps/agent-tars/src/renderer/src/components/LeftSidebar/Settings/modelUtils.tsx`

#### 集成验证
我们已经验证了以下功能：
- ✅ Ollama服务连接正常
- ✅ qwen2.5vl:3b模型可用
- ✅ OpenAI兼容API工作正常
- ✅ 前端UI支持完整
- ✅ 所有集成代码已实现

## 🔧 如果坚持要构建源码

### 解决构建问题的步骤

1. **以管理员身份运行PowerShell**
2. **设置正确的环境**：
   ```powershell
   # 设置编码
   chcp 65001
   $env:NODE_OPTIONS="--max-old-space-size=8192"
   
   # 安装依赖
   pnpm install --ignore-scripts
   
   # 构建
   pnpm run dev:agent-tars
   ```

3. **如果electron-vite问题继续**：
   ```powershell
   # 尝试手动安装electron-vite
   cd apps/agent-tars
   pnpm add electron-vite -D
   
   # 或使用npm
   npm install electron-vite --save-dev
   ```

## 📋 完整集成内容

我们的Ollama集成包含：

### 功能特性
- 🔌 完整的OpenAI API兼容
- 🔄 支持流式响应
- 🛠️ 工具/函数调用支持
- 👁️ 视觉-语言模型支持
- 🔒 本地推理，保护隐私
- 🌐 离线操作支持

### 模型支持
- qwen2.5vl:3b (您当前的模型)
- llama3.2-vision
- llama3.1
- 其他Ollama支持的模型

### UI集成
- Ollama图标显示
- 预定义模型选项
- 自定义端点配置
- 连接状态检测

## 🚀 推荐使用流程

1. **下载预构建版本** (最简单)
2. **安装并启动**
3. **配置Ollama设置**：
   - Provider: Ollama
   - Model: qwen2.5vl:3b
   - API Key: ollama
   - Endpoint: http://localhost:11434/v1
4. **开始使用** GUI自动化功能

## 📞 支持文档

- 查看：`启动和配置指南.md`
- 参考：`docs/ollama-integration.md`
- 配置：`apps/agent-tars/ollama-config.example.env`
- 快速开始：`OLLAMA_QUICKSTART.md`

---

**注意**：如果您当前的构建问题无法解决，强烈建议使用预构建版本，它包含了我们所有的Ollama集成功能，可以立即使用。 