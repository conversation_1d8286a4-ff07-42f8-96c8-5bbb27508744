{"name": "@agent-infra/mcp-shared", "version": "1.1.1", "description": "MCP shared", "license": "MIT", "homepage": "https://github.com/bytedance/UI-TARS-desktop", "bugs": "https://github.com/bytedance/UI-TARS-desktop/issues", "repository": {"type": "git", "url": "**************:bytedance/UI-TARS-desktop.git", "directory": "packages/agent-infra/mcp-shared"}, "main": "./dist/index.js", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "exports": {".": {"import": "./dist/index.mjs", "require": "./dist/index.js", "types": "./dist/index.d.ts"}, "./client": {"import": "./dist/client/index.mjs", "require": "./dist/client/index.js", "types": "./dist/client/index.d.ts"}, "./server": {"import": "./dist/server/index.mjs", "require": "./dist/server/index.js", "types": "./dist/server/index.d.ts"}, "./package.json": "./package.json"}, "files": ["dist"], "scripts": {"build": "shx rm -rf dist && rslib build", "prepare": "npm run build", "watch": "rslib build --watch"}, "dependencies": {"@modelcontextprotocol/sdk": "^1.11.2", "zod": "^3.23.8"}, "devDependencies": {"@agent-infra/logger": "workspace:*", "cross-fetch": "4.1.0", "@rslib/core": "0.5.3", "tsx": "^4.19.3", "vitest": "^3.0.7", "@types/diff": "^5.0.9", "@types/minimatch": "^5.1.2", "@types/node": "^22", "typescript": "^5.7.3"}}