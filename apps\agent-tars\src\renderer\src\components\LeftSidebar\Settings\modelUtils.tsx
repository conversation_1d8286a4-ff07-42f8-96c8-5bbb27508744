import {
  SiGooglegemini,
  SiOpenai,
  SiAnthropic,
  SiMicrosoftazure,
} from 'react-icons/si';
import DeepSeekIcon from '@renderer/assets/DeepSeekIcon';
import { AiFillApi } from 'react-icons/ai';
import MistralIcon from '@renderer/assets/Mistral';
import { ModelProvider } from '@agent-infra/shared';

export function getProviderLogo(provider: ModelProvider) {
  switch (provider) {
    case ModelProvider.OPENAI:
      return <SiOpenai size={18} />;
    case ModelProvider.ANTHROPIC:
      return <SiAnthropic size={18} />;
    case ModelProvider.GEMINI:
      return <SiGooglegemini size={18} />;
    case ModelProvider.MISTRAL:
      return <MistralIcon />;
    case ModelProvider.AZURE_OPENAI:
      return <SiMicrosoftazure size={18} />;
    case ModelProvider.DEEPSEEK:
      return <DeepSeekIcon />;
    case ModelProvider.OLLAMA:
      return <AiFillApi size={18} />;
    default:
      return <AiFillApi size={18} />;
  }
}

export function getModelOptions(provider: ModelProvider) {
  switch (provider) {
    case ModelProvider.ANTHROPIC:
      return [
        { value: 'claude-3-7-sonnet-latest', label: 'Claude 3.7 Sonnet' },
        { value: 'claude-3-5-sonnet-20241022', label: 'Claude 3.5 Sonnet' },
      ];
    case ModelProvider.OPENAI:
      return [
        { value: 'gpt-4o', label: 'GPT-4o' },
        { value: 'gpt-4o-mini', label: 'GPT-4o Mini' },
      ];
    case ModelProvider.GEMINI:
      return [
        { value: 'gemini-2.0-flash', label: 'Gemini 2.0 Flash' },
        { value: 'gemini-1.5-pro', label: 'Gemini 1.5 Pro' },
        { value: 'gemini-1.5-flash', label: 'Gemini 1.5 Flash' },
      ];
    case ModelProvider.MISTRAL:
      return [
        { value: 'mistral-large-latest', label: 'Mistral Large' },
        { value: 'mistral-medium-latest', label: 'Mistral Medium' },
        { value: 'mistral-small-latest', label: 'Mistral Small' },
      ];
    case ModelProvider.DEEPSEEK:
      return [{ value: 'deepseek-chat', label: 'DeepSeek-V3' }];
    case ModelProvider.OLLAMA:
      return [
        { value: 'qwen2.5vl:3b', label: 'Qwen2.5VL 3B' },
        { value: 'qwen2.5vl:7b', label: 'Qwen2.5VL 7B' },
        { value: 'qwen2.5vl:32b', label: 'Qwen2.5VL 32B' },
        { value: 'llama3.2-vision:11b', label: 'Llama 3.2 Vision 11B' },
        { value: 'llama3.2-vision:90b', label: 'Llama 3.2 Vision 90B' },
        { value: 'minicpm-v:8b', label: 'MiniCPM-V 8B' },
      ];
    case ModelProvider.AZURE_OPENAI:
      return [];
    default:
      return [];
  }
}
