{"name": "ui-tars-desktop-renderer", "version": "0.0.1", "private": true, "dependencies": {"@chakra-ui/react": "2.10.4", "@chakra-ui/theme-tools": "^2.2.6", "@emotion/react": "^11.13.3", "@emotion/styled": "^11.13.0", "@hookform/resolvers": "^5.0.1", "@radix-ui/react-alert-dialog": "^1.1.7", "@radix-ui/react-avatar": "^1.1.4", "@radix-ui/react-collapsible": "^1.1.4", "@radix-ui/react-dialog": "^1.1.7", "@radix-ui/react-dropdown-menu": "^2.1.7", "@radix-ui/react-label": "^2.1.3", "@radix-ui/react-scroll-area": "^1.2.4", "@radix-ui/react-select": "^2.1.7", "@radix-ui/react-separator": "^1.1.3", "@radix-ui/react-slider": "^1.2.4", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-switch": "^1.1.4", "@radix-ui/react-tabs": "^1.1.4", "@radix-ui/react-tooltip": "^1.2.0", "class-variance-authority": "0.7.1", "clsx": "2.1.1", "copy-to-clipboard": "3.3.3", "dayjs": "1.11.13", "formik": "2.4.6", "framer-motion": "^11.11.9", "idb-keyval": "6.2.1", "lucide-react": "0.487.0", "markdown-it": "14.1.0", "medium-zoom": "1.1.0", "ms": "^2.1.3", "next-themes": "^0.4.6", "qs": "6.13.1", "react": "19.1.0", "react-dom": "19.1.0", "react-hook-form": "7.46.1", "react-icons": "^5.3.0", "react-router": "^7.1.1", "react-use": "^17.6.0", "sonner": "^2.0.3", "swr": "^2.3.0", "tailwind-merge": "3.1.0", "tw-animate-css": "1.2.5", "uuid": "11.1.0", "zod": "^3.24.2", "zustand": "^5.0.0"}, "devDependencies": {"@types/react": "19.1.2", "@types/react-dom": "19.1.1", "electron": "34.1.1"}}