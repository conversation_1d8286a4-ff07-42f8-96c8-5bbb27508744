# UI-TARS + Ollama 启动和配置指南

## 🚀 快速启动

### 1. 确保Ollama运行
确保您的Ollama服务正在运行，并且qwen2.5vl:3b模型可用：

```bash
# 检查Ollama状态
curl http://localhost:11434/api/tags

# 如果没有运行，启动Ollama
ollama serve
```

### 2. 启动UI-TARS应用

在项目根目录下运行以下命令之一：

**方法1: 使用npx（推荐）**
```bash
npx turbo run agent-tars-app#dev
```

**方法2: 如果上面命令有问题，尝试**
```bash
cd apps/agent-tars
npm run dev
```

**方法3: 全局安装turbo**
```bash
npm install -g turbo
turbo run agent-tars-app#dev
```

### 3. 在应用中配置Ollama

应用启动后，进行以下配置：

1. **打开设置页面**
   - 在应用中找到设置（Settings）菜单

2. **配置模型设置**
   - **Provider（提供商）**: 选择 "Ollama"
   - **Model（模型）**: 输入 "qwen2.5vl:3b"
   - **API Key（API密钥）**: 输入 "ollama"
   - **Endpoint（端点）**: 输入 "http://localhost:11434/v1"

3. **保存设置**
   - 点击保存按钮确认配置

### 4. 测试连接

配置完成后，应用应该能够成功连接到您的本地Ollama模型。

## 🔧 故障排除

### 问题1: "turbo命令未找到"
**解决方案:**
```bash
# 安装turbo
npm install -g turbo
# 或者使用npx
npx turbo run agent-tars-app#dev
```

### 问题2: "electron-vite命令未找到"
**解决方案:**
```bash
# 在agent-tars目录中安装依赖
cd apps/agent-tars
npm install
```

### 问题3: 应用无法连接到Ollama
**解决方案:**
1. 确保Ollama正在运行: `ollama serve`
2. 检查端口11434未被占用
3. 验证配置的端点: `http://localhost:11434/v1`
4. 确认模型名称: `qwen2.5vl:3b`

### 问题4: 依赖安装失败
**解决方案:**
```bash
# 清理缓存后重新安装
npm cache clean --force
npm install
```

## 📋 配置检查清单

- [ ] Ollama服务正在运行
- [ ] qwen2.5vl:3b模型已下载并可用
- [ ] UI-TARS应用成功启动
- [ ] 在设置中选择了"Ollama"提供商
- [ ] 正确配置了模型名称和端点
- [ ] 保存了设置

## 🎯 成功标志

当一切配置正确时，您应该能够：
- 在应用设置中看到"Ollama"选项
- 成功保存Ollama配置
- 使用本地AI模型进行GUI自动化任务
- 享受完全离线的AI助手体验

## 📞 获取帮助

如果遇到问题：
1. 查看控制台错误信息
2. 参考详细文档: `docs/ollama-integration.md`
3. 检查Ollama日志
4. 在GitHub上提交issue

🎉 祝您使用愉快！ 