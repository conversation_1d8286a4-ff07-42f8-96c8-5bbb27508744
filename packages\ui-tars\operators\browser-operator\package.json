{"name": "@ui-tars/operator-browser", "description": "Native-browser operator for UI-TARS", "version": "1.2.1", "repository": {"type": "git", "url": "https://github.com/bytedance/UI-TARS-desktop"}, "bugs": {"url": "https://github.com/bytedance/UI-TARS-desktop/issues"}, "keywords": ["AI", "Core", "SDK", "Operator", "UI-TARS"], "main": "./dist/index.js", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "scripts": {"dev": "rslib build --watch", "build": "rslib build", "build:watch": "rslib build --watch", "prepare": "npm run build", "test": "vitest"}, "license": "Apache-2.0", "files": ["dist"], "publishConfig": {"access": "public", "registry": "https://registry.npmjs.org"}, "dependencies": {"@agent-infra/browser": "workspace:*", "@agent-infra/logger": "workspace:*", "@ui-tars/sdk": "workspace:*"}, "devDependencies": {"node-notifier": "10.0.1", "@agent-infra/browser": "workspace:*", "@ui-tars/sdk": "workspace:*", "@common/configs": "workspace:*", "@rslib/core": "^0.5.4", "typescript": "^5.7.2", "vitest": "^3.0.2", "ts-node": "^10.9.2", "@types/big.js": "^6.2.2"}}