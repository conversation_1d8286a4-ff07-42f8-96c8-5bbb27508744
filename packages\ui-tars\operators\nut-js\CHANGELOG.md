# @ui-tars/operator-nut-js

## 1.2.1

### Patch Changes

- @ui-tars/sdk@1.2.1
- @ui-tars/shared@1.2.1

## 1.2.1-beta.0

### Patch Changes

- @ui-tars/sdk@1.2.1-beta.0
- @ui-tars/shared@1.2.1-beta.0

## 1.2.0

### Patch Changes

- c050e6a: chore: same version
- 4e0883f: chore: open-operator
- 2bf2f4a: feat(sdk): action parser add start_coords, end_coords
- 2bf2f4a: refactor: operator no need return width and height
- 2bf2f4a: chore: screenshot bug
- 2bf2f4a: fix: test
- c050e6a: feat: sdk factors version
- 107c049: bump: sdk support
- d80285d: chore: publish adb
- 4e0883f: chore: types
- 2b9c631: chore: changeset
- d80285d: feat: ui-tars add adb operator
- 5fb2821: feat: ui-tars 1.5
- Updated dependencies [c050e6a]
- Updated dependencies [4e0883f]
- Updated dependencies [2bf2f4a]
- Updated dependencies [2bf2f4a]
- Updated dependencies [2bf2f4a]
- Updated dependencies [c050e6a]
- Updated dependencies [c050e6a]
- Updated dependencies [2bf2f4a]
- Updated dependencies [c050e6a]
- Updated dependencies [107c049]
- Updated dependencies [d80285d]
- Updated dependencies [4e0883f]
- Updated dependencies [2b9c631]
- Updated dependencies [d80285d]
- Updated dependencies [5fb2821]
- Updated dependencies [d169e46]
  - @ui-tars/shared@1.2.0
  - @ui-tars/sdk@1.2.0

## 1.2.0-beta.24

### Patch Changes

- chore: publish adb
- Updated dependencies
  - @ui-tars/shared@1.2.0-beta.24
  - @ui-tars/sdk@1.2.0-beta.24

## 1.2.0-beta.23

### Patch Changes

- feat: ui-tars add adb operator
- Updated dependencies
  - @ui-tars/sdk@1.2.0-beta.23
  - @ui-tars/shared@1.2.0-beta.23

## 1.2.0-beta.22

### Patch Changes

- chore: changeset
- Updated dependencies
  - @ui-tars/sdk@1.2.0-beta.22
  - @ui-tars/shared@1.2.0-beta.22

## 1.2.0-beta.21

### Patch Changes

- fix: test
- Updated dependencies
  - @ui-tars/shared@1.2.0-beta.21
  - @ui-tars/sdk@1.2.0-beta.21

## 1.2.0-beta.20

### Patch Changes

- feat(sdk): action parser add start_coords, end_coords
- Updated dependencies
  - @ui-tars/shared@1.2.0-beta.20
  - @ui-tars/sdk@1.2.0-beta.20

## 1.2.0-beta.19

### Patch Changes

- chore: screenshot bug
- Updated dependencies
  - @ui-tars/shared@1.2.0-beta.19
  - @ui-tars/sdk@1.2.0-beta.19

## 1.2.0-beta.18

### Patch Changes

- refactor: operator no need return width and height
- Updated dependencies
  - @ui-tars/shared@1.2.0-beta.18
  - @ui-tars/sdk@1.2.0-beta.18

## 1.2.0-beta.17

### Patch Changes

- chore: same version
- Updated dependencies
  - @ui-tars/shared@1.2.0-beta.17
  - @ui-tars/sdk@1.2.0-beta.17

## 1.2.0-beta.15

### Patch Changes

- feat: sdk factors version
- Updated dependencies
  - @ui-tars/sdk@1.2.0-beta.16
  - @ui-tars/shared@1.2.0-beta.12

## 1.2.0-beta.14

### Patch Changes

- Updated dependencies
  - @ui-tars/sdk@1.2.0-beta.15

## 1.2.0-beta.13

### Patch Changes

- Updated dependencies
  - @ui-tars/sdk@1.2.0-beta.13

## 1.2.0-beta.12

### Patch Changes

- chore: open-operator
- Updated dependencies
  - @ui-tars/shared@1.2.0-beta.11
  - @ui-tars/sdk@1.2.0-beta.12

## 1.2.0-beta.11

### Patch Changes

- chore: types
- Updated dependencies
  - @ui-tars/sdk@1.2.0-beta.11

## 1.2.0-beta.10

### Patch Changes

- Updated dependencies
  - @ui-tars/sdk@1.2.0-beta.10
  - @ui-tars/shared@1.2.0-beta.10

## 1.2.0-beta.9

### Patch Changes

- bump: sdk support
- Updated dependencies
  - @ui-tars/sdk@1.2.0-beta.9
  - @ui-tars/shared@1.2.0-beta.9

## 1.2.0-beta.8

### Patch Changes

- fix: useConfig to useContext
- Updated dependencies
  - @ui-tars/sdk@1.2.0-beta.8

## 1.2.0-beta.7

### Patch Changes

- Updated dependencies
  - @ui-tars/sdk@1.2.0-beta.7

## 1.2.0-beta.6

### Patch Changes

- feat: new sdk
- Updated dependencies
  - @ui-tars/shared@1.2.0-beta.6
  - @ui-tars/sdk@1.2.0-beta.6

## 1.2.0-beta.5

### Patch Changes

- chore: update sdk
- Updated dependencies
  - @ui-tars/shared@1.2.0-beta.5
  - @ui-tars/sdk@1.2.0-beta.5

## 1.2.0-beta.4

### Patch Changes

- chore: new version
- Updated dependencies
  - @ui-tars/shared@1.2.0-beta.4
  - @ui-tars/sdk@1.2.0-beta.4

## 1.2.0-beta.3

### Patch Changes

- chore: add retry
- Updated dependencies
  - @ui-tars/shared@1.2.0-beta.3
  - @ui-tars/sdk@1.2.0-beta.3

## 1.2.0-beta.2

### Patch Changes

- chore: publish
- Updated dependencies
  - @ui-tars/shared@1.2.0-beta.2
  - @ui-tars/sdk@1.2.0-beta.2

## 1.2.0-beta.1

### Patch Changes

- chore: remove unused code
- Updated dependencies
  - @ui-tars/shared@1.2.0-beta.1
  - @ui-tars/sdk@1.2.0-beta.1

## 1.2.0-beta.0

### Minor Changes

- a062e03: feat: ui-tars agent sdk support

### Patch Changes

- Updated dependencies [a062e03]
  - @ui-tars/shared@1.2.0-beta.0
  - @ui-tars/sdk@1.2.0-beta.0
