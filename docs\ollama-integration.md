# Ollama Integration Guide for UI-TARS Desktop

本指南将帮助您在UI-TARS桌面应用中集成和使用Ollama本地模型。

## 目录

- [简介](#简介)
- [先决条件](#先决条件)
- [安装和配置](#安装和配置)
- [在UI-TARS中配置Ollama](#在ui-tars中配置ollama)
- [支持的模型](#支持的模型)
- [故障排除](#故障排除)

## 简介

Ollama是一个本地AI模型运行时，可以让您在自己的计算机上运行大型语言模型。通过集成Ollama，UI-TARS可以使用本地模型，确保数据隐私和离线使用能力。

## 先决条件

1. **系统要求**
   - Windows 10/11, macOS 10.12+, 或 Linux
   - 至少8GB RAM（推荐16GB+）
   - 支持CUDA的GPU（可选，但强烈推荐用于更好的性能）

2. **已安装的软件**
   - [Ollama](https://ollama.ai/) - 从官网下载并安装

## 安装和配置

### 步骤 1: 安装 Ollama

1. 访问 [Ollama官网](https://ollama.ai/)
2. 下载适用于您操作系统的安装包
3. 按照安装向导完成安装

### 步骤 2: 下载模型

您已经下载了 `qwen2.5vl:3b` 模型，这是一个优秀的视觉语言模型。如果需要下载其他模型：

```bash
# 下载 Qwen2.5VL 3B（您已有的模型）
ollama pull qwen2.5vl:3b

# 其他可用的视觉模型
ollama pull qwen2.5vl:7b        # 更大的Qwen模型
ollama pull llama3.2-vision:11b  # Llama 3.2 Vision
ollama pull minicpm-v:8b         # MiniCPM-V
```

### 步骤 3: 启动 Ollama 服务

```bash
# 启动 Ollama 服务（如果没有作为系统服务运行）
ollama serve
```

默认情况下，Ollama在 `http://localhost:11434` 运行。

### 步骤 4: 验证安装

```bash
# 列出已安装的模型
ollama list

# 测试模型
ollama run qwen2.5vl:3b "Hello, how are you?"
```

## 在UI-TARS中配置Ollama

### 方法 1: 通过图形界面配置

1. 启动 UI-TARS 桌面应用
2. 进入 **设置** (Settings)
3. 在 **模型设置** (Model Settings) 部分：
   - **Provider**: 选择 "Ollama"
   - **Model**: 选择 "qwen2.5vl:3b" 或您下载的其他模型
   - **API Key**: 输入 "ollama"（Ollama不需要真实的API密钥）
   - **Endpoint**: 输入 "http://localhost:11434/v1"

### 方法 2: 通过环境变量配置

创建或编辑 `.env` 文件：

```env
# Ollama 配置
OLLAMA_API_KEY=ollama
OLLAMA_BASE_URL=http://localhost:11434/v1
OLLAMA_DEFAULT_MODEL=qwen2.5vl:3b
```

您也可以参考项目中的 `apps/agent-tars/ollama-config.example.env` 文件。

## 支持的模型

UI-TARS的Ollama集成支持以下视觉语言模型：

| 模型名称 | 大小 | 描述 | 推荐用途 |
|---------|------|------|----------|
| `qwen2.5vl:3b` | ~2GB | 轻量级视觉语言模型 | 快速响应，资源有限的设备 |
| `qwen2.5vl:7b` | ~4GB | 平衡的视觉语言模型 | 一般用途，性能与速度平衡 |
| `qwen2.5vl:32b` | ~18GB | 大型视觉语言模型 | 最佳性能，需要高端硬件 |
| `llama3.2-vision:11b` | ~6GB | Meta的视觉模型 | 图像理解和分析 |
| `llama3.2-vision:90b` | ~50GB | 超大型视觉模型 | 专业用途，需要强大硬件 |
| `minicpm-v:8b` | ~5GB | 高效的视觉模型 | 移动和边缘设备 |

## 故障排除

### 常见问题

#### 1. 连接错误
**错误**: "Failed to connect to Ollama"
**解决方案**:
- 确保Ollama服务正在运行：`ollama serve`
- 检查端口11434是否被占用
- 验证URL配置：`http://localhost:11434/v1`

#### 2. 模型未找到
**错误**: "Model not found"
**解决方案**:
- 检查模型是否已下载：`ollama list`
- 确保模型名称拼写正确（区分大小写）
- 重新下载模型：`ollama pull qwen2.5vl:3b`

#### 3. 内存不足
**错误**: 模型加载失败或响应缓慢
**解决方案**:
- 使用更小的模型（如3B而不是32B）
- 关闭其他内存密集型应用程序
- 考虑升级RAM

#### 4. GPU支持问题
**问题**: 模型运行在CPU上，速度较慢
**解决方案**:
- 安装适当的GPU驱动程序
- 对于NVIDIA GPU，安装CUDA工具包
- 检查Ollama是否检测到GPU：重启Ollama服务

### 性能优化

1. **硬件建议**:
   - GPU: NVIDIA RTX 3060或更高（用于大型模型）
   - RAM: 16GB+（用于多个并发请求）
   - 存储: SSD（更快的模型加载）

2. **配置优化**:
   ```bash
   # 设置GPU内存限制（如果需要）
   export OLLAMA_MAX_VRAM=8GB
   
   # 并发请求限制
   export OLLAMA_MAX_QUEUE=5
   ```

3. **模型选择**:
   - 开发和测试：使用 `qwen2.5vl:3b`
   - 生产环境：使用 `qwen2.5vl:7b` 或更大模型
   - 高端硬件：可尝试 `qwen2.5vl:32b`

### 调试模式

启用详细日志记录：

```bash
export OLLAMA_DEBUG=1
ollama serve
```

这将提供更详细的错误信息，帮助诊断问题。

## 后续步骤

配置完成后，您可以：

1. 在UI-TARS中测试GUI自动化功能
2. 尝试不同的模型以找到最适合您需求的模型
3. 根据使用模式优化配置

## 支持和反馈

如果您遇到问题：

1. 检查[UI-TARS GitHub Issues](https://github.com/bytedance/UI-TARS-desktop/issues)
2. 查看[Ollama文档](https://github.com/ollama/ollama)
3. 在项目仓库中提交新的issue

享受使用本地AI模型的强大功能！ 