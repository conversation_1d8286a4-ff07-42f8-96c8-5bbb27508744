{"name": "@ui-tars/sdk", "version": "1.2.1", "description": "A powerful cross-platform(ANY device/platform) toolkit for building GUI automation agents for UI-TARS", "repository": {"type": "git", "url": "https://github.com/bytedance/UI-TARS-desktop"}, "bugs": {"url": "https://github.com/bytedance/UI-TARS-desktop/issues"}, "keywords": ["AI", "SDK", "Operator", "UI-TARS"], "main": "./dist/index.js", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "sideEffects": false, "browser": {"./": "./dist/index.mjs", "./core": "./dist/core.mjs", "./constants": "./dist/constants.mjs"}, "exports": {".": {"import": "./dist/index.mjs", "types": "./dist/index.d.ts", "require": "./dist/index.js"}, "./core": {"import": "./dist/core.mjs", "types": "./dist/core.d.ts", "require": "./dist/core.js"}, "./constants": {"import": "./dist/constants.mjs", "types": "./dist/constants.d.ts", "require": "./dist/constants.js"}}, "scripts": {"dev": "rslib build --watch", "prepare": "tsx scripts/sync-docs.ts && npm run build", "build": "rslib build", "build:watch": "rslib build --watch", "test:browser": "vitest --environment happy-dom", "test": "vitest --environment happy-dom && vitest --environment node"}, "license": "Apache-2.0", "files": ["dist"], "publishConfig": {"access": "public", "registry": "https://registry.npmjs.org"}, "dependencies": {"openai": "4.85.1", "jimp": "1.6.0", "async-retry": "1.3.3", "@ui-tars/shared": "workspace:*", "@ui-tars/action-parser": "workspace:*"}, "devDependencies": {"tsx": "^4.19.2", "@rslib/core": "^0.5.4", "@common/configs": "workspace:*", "typescript": "^5.7.2", "vitest": "^3.0.2", "react": "^18.3.1", "react-dom": "^18.3.1", "@vitejs/plugin-react": "^4.3.4", "vitest-browser-react": "^0.1.1", "@types/async-retry": "1.4.9", "happy-dom": "^17.1.1"}}