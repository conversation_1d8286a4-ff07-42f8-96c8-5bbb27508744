{"name": "agent-tars-app", "private": true, "version": "1.0.0-alpha.8", "description": "A multimodal AI agent that revolutionizes GUI interaction", "main": "./dist/main/index.js", "author": "ByteDance", "homepage": "https://electron-vite.org", "packageManager": "pnpm@9.12.3", "scripts": {"typecheck:node": "tsc --noEmit -p tsconfig.node.json --composite false", "typecheck:web": "tsc --noEmit -p tsconfig.web.json --composite false", "typecheck": "npm run typecheck:node && npm run typecheck:web", "asar:analyze": "asar extract out/Agent\\ TARS-darwin-arm64/Agent\\ TARS.app/Contents/Resources/app.asar ./dist/asar", "start": "electron-vite preview", "dev": "electron-vite dev", "prepare": "npm run build:reporter", "package": "electron-forge package", "build": "rimraf dist out && npm run typecheck && npm run build:reporter && electron-vite build && electron-forge make", "test": "vitest run", "build:dist": "cross-env NODE_ENV=production electron-vite build", "build:e2e": "npm run build:dist && cross-env CI=e2e npm run package", "test:e2e": "playwright test", "publish:mac": "npm run build:reporter && electron-vite build && electron-forge publish --arch=universal --platform=darwin", "dev:reporter": "vite dev", "build:reporter": "vite build"}, "peerDependencies": {"esbuild-register": "*", "ts-node": "*", "tsx": "*"}, "peerDependenciesMeta": {"esbuild-register": {"optional": true}, "ts-node": {"optional": true}, "tsx": {"optional": true}}, "dependencies": {"@tavily/core": "0.3.1", "@computer-use/node-mac-permissions": "2.2.2", "@agent-infra/mcp-server-commands": "workspace:*", "@agent-infra/mcp-server-filesystem": "workspace:*", "@agent-infra/mcp-server-browser": "workspace:*"}, "devDependencies": {"jsonrepair": "3.12.0", "serialize-javascript": "6.0.2", "@modelcontextprotocol/sdk": "^1.11.2", "@electron-toolkit/preload": "^3.0.1", "@electron-toolkit/utils": "^4.0.0", "@electron/asar": "^3.2.18", "electron-updater": "^6.3.9", "openai": "^4.86.2", "dotenv": "16.4.7", "@agent-infra/mcp-shared": "workspace:*", "@agent-infra/shared": "workspace:*", "@agent-infra/logger": "workspace:*", "@agent-infra/mcp-client": "workspace:*", "@agent-infra/search": "workspace:*", "ws": "8.18.1", "utf-8-validate": "6.0.5", "bufferutil": "4.0.9", "fs-extra": "11.3.0", "@anthropic-ai/sdk": "0.39.0", "@google/generative-ai": "0.24.0", "@mistralai/mistralai": "1.5.1", "@ui-tars/electron-ipc": "workspace:*", "@common/electron-build": "workspace:*", "@electron-forge/cli": "^7.7.0", "@electron-forge/maker-deb": "^7.7.0", "@electron-forge/maker-dmg": "^7.7.0", "@electron-forge/maker-rpm": "^7.7.0", "@electron-forge/maker-squirrel": "^7.7.0", "@electron-forge/maker-zip": "^7.7.0", "@electron-forge/plugin-auto-unpack-natives": "^7.7.0", "@electron-forge/plugin-fuses": "^7.7.0", "@electron-forge/plugin-vite": "^7.7.0", "@electron-forge/publisher-github": "^7.7.0", "@electron-toolkit/tsconfig": "^1.0.1", "@types/node": "^22.13.4", "@types/fs-extra": "11.0.4", "@vitejs/plugin-react": "^4.3.4", "@playwright/test": "^1.49.1", "cross-env": "^7.0.3", "electron-playwright-helpers": "^1.7.1", "rimraf": "^6.0.1", "autoprefixer": "10.4.20", "electron": "34.1.1", "electron-store": "^10.0.0", "electron-packager-languages": "0.6.0", "electron-vite": "^3.0.0", "jsdom": "^26.0.0", "sass": "1.85.1", "tailwindcss": "^3.3.3", "typescript": "^5.7.3", "vite": "^6.1.0", "vite-tsconfig-paths": "^5.1.4", "vitest": "^3.0.8", "vite-plugin-singlefile": "2.2.0"}, "build": {"publish": [{"provider": "github", "owner": "bytedance", "repo": "UI-TARS-desktop"}], "electronDownload": {"mirror": "https://npmmirror.com/mirrors/electron/"}}}