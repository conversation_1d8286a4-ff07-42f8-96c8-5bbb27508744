on:
  pull_request:
    types: [opened, synchronize, reopened]
    branches:
      - '**'
    paths:
      - 'packages/agent-infra/mcp-*/**'
      - '.github/workflows/benchmark.yml'

permissions:
  id-token: write
  contents: read
  attestations: read

name: Benchmark
concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}-test
  cancel-in-progress: true
env:
  CI: true
  NODE_OPTIONS: --max-old-space-size=8192
  HUSKY: 0

jobs:
  mcp_benchmark:
    name: Benchmark
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2

      - name: Cache turbo
        uses: actions/cache@d4323d4df104b026a6aa633fdb11d772146be0bf
        with:
          path: .turbo
          key: ${{ runner.os }}-turbo-${{ github.sha }}
          restore-keys: |
            ${{ runner.os }}-turbo-

      - name: Install pnpm
        run: npm install -g pnpm@9
      - uses: actions/setup-node@1d0ff469b7ec7b3cb9d8673fde0c81c44821de2a # v4.2.0
        with:
          node-version: 20
          cache: 'pnpm'
      - name: Install Python dependencies
        run: |
          sudo apt-get update
          sudo apt-get install -y python3-pip
          pip3 install --upgrade pip
          pip3 install uv
      - name: Install dependencies
        run: pnpm install
      - name: Run MCP benchmark
        run: cd packages/agent-infra/mcp-benchmark && pnpm run benchmark
