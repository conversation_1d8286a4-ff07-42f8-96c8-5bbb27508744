{"name": "@agent-infra/duckduckgo-search", "version": "0.0.1", "main": "dist/index.js", "module": "dist/index.mjs", "types": "dist/index.d.ts", "exports": {".": {"import": "./dist/index.mjs", "require": "./dist/index.js", "types": "./dist/index.d.ts"}}, "files": ["dist"], "scripts": {"dev": "rslib build --watch", "build": "rslib build", "prepare": "npm run build", "prepublishOnly": "pnpm run build", "test": "vitest run", "test:watch": "vitest", "test:e2e": "vitest --config vitest.e2e.config.ts", "coverage": "vitest run --coverage", "run:example": "ts-node scripts/example.ts"}, "dependencies": {"async-retry": "1.3.3", "duck-duck-scrape": "^2.2.7", "@agent-infra/logger": "workspace:*"}, "devDependencies": {"@types/async-retry": "1.4.9", "@types/node": "20.14.8", "rimraf": "4.1.0", "typescript": "4.9.4", "vitest": "3.0.7", "@vitest/coverage-v8": "3.0.7", "ts-node": "10.9.2", "@rslib/core": "0.5.3"}}