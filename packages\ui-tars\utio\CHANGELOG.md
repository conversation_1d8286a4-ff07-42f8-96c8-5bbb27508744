# @ui-tars/utio

## 1.2.1

## 1.2.1-beta.0

## 1.2.0

### Patch Changes

- c050e6a: chore: same version
- 2bf2f4a: feat(sdk): action parser add start_coords, end_coords
- 2bf2f4a: refactor: operator no need return width and height
- 2bf2f4a: chore: screenshot bug
- 2bf2f4a: fix: test
- c050e6a: feat: sdk factors version
- 107c049: bump: sdk support
- d80285d: chore: publish adb
- 2b9c631: chore: changeset
- d80285d: feat: ui-tars add adb operator
- 5fb2821: feat: ui-tars 1.5

## 1.2.0-beta.24

### Patch Changes

- chore: publish adb

## 1.2.0-beta.23

### Patch Changes

- feat: ui-tars add adb operator

## 1.2.0-beta.22

### Patch Changes

- chore: changeset

## 1.2.0-beta.21

### Patch Changes

- fix: test

## 1.2.0-beta.20

### Patch Changes

- feat(sdk): action parser add start_coords, end_coords

## 1.2.0-beta.19

### Patch Changes

- chore: screenshot bug

## 1.2.0-beta.18

### Patch Changes

- refactor: operator no need return width and height

## 1.2.0-beta.17

### Patch Changes

- chore: same version

## 1.2.0-beta.11

### Patch Changes

- feat: sdk factors version

## 1.2.0-beta.10

## 1.2.0-beta.9

### Patch Changes

- bump: sdk support

## 1.2.0-beta.6

### Patch Changes

- feat: new sdk

## 1.2.0-beta.5

### Patch Changes

- chore: update sdk

## 1.1.0-beta.4

### Patch Changes

- chore: new version

## 1.1.0-beta.3

### Patch Changes

- chore: add retry

## 1.1.0-beta.2

### Patch Changes

- chore: publish

## 1.1.0-beta.1

### Patch Changes

- chore: remove unused code

## 1.1.0-beta.0

### Minor Changes

- a062e03: feat: ui-tars agent sdk support
