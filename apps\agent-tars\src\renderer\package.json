{"name": "agent-tars-app-renderer", "private": true, "version": "0.0.1", "dependencies": {"@ui-tars/electron-ipc": "workspace:*", "react-hook-form": "7.46.1", "react": "^18.3.1", "react-dom": "^18.3.1", "@nextui-org/react": "2.4.8", "framer-motion": "11.2.13", "styled-components": "6.1.11", "localforage": "^1.10.0", "swr": "^2.3.3", "jotai": "^2.8.2", "react-icons": "5.2.1", "lucide-react": "0.479.0", "classnames": "2.5.1", "jszip": "3.10.1", "@nextui-org/system": "2.4.6", "@nextui-org/theme": "2.4.5", "@agent-infra/mcp-shared": "workspace:*", "@agent-infra/shared": "workspace:*", "clsx": "2.1.1", "tailwind-merge": "3.0.2", "use-dark-mode": "2.3.1", "eventemitter3": "5.0.1", "dayjs": "1.11.13", "date-fns": "4.1.0", "react-split": "2.0.14", "@monaco-editor/react": "4.7.0", "xterm": "^5.3.0", "xterm-addon-fit": "^0.8.0", "xterm-addon-web-links": "^0.9.0", "jsonrepair": "3.12.0", "react-markdown": "10.1.0", "remark-gfm": "4.0.1", "remark-math": "6.0.0", "rehype-katex": "7.0.1", "lodash-es": "4.17.21", "katex": "0.16.21", "react-syntax-highlighter": "15.5.0", "uuid": "11.1.0", "react-hot-toast": "2.5.2", "path-browserify": "1.0.1"}, "devDependencies": {"@modelcontextprotocol/sdk": "^1.11.2", "typescript": "^5.7.3", "@types/react": "^18.3.18", "@types/react-dom": "^18.3.5", "@tailwindcss/aspect-ratio": "0.4.2", "@agent-infra/search": "workspace:*"}}