{"name": "@ui-tars/action-parser", "version": "1.2.1", "description": "Action parser SDK for UI-TARS", "repository": {"type": "git", "url": "https://github.com/bytedance/UI-TARS-desktop"}, "bugs": {"url": "https://github.com/bytedance/UI-TARS-desktop/issues"}, "keywords": ["AI", "Action", "<PERSON><PERSON><PERSON>", "UI-TARS"], "main": "./dist/index.js", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "scripts": {"dev": "rslib build --watch", "build": "rslib build", "build:watch": "rslib build --watch", "prepare": "npm run build", "test": "vitest"}, "license": "Apache-2.0", "files": ["dist"], "publishConfig": {"access": "public", "registry": "https://registry.npmjs.org"}, "dependencies": {"@ui-tars/shared": "workspace:*", "lodash.isnumber": "3.0.3"}, "devDependencies": {"@rslib/core": "^0.5.4", "@types/lodash.isnumber": "3.0.3", "@common/configs": "workspace:*", "typescript": "^5.7.2", "vitest": "^3.0.2"}}