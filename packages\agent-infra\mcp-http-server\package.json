{"name": "mcp-http-server", "version": "1.1.1", "description": "High performance HTTP Server for MCP", "license": "MIT", "homepage": "https://github.com/bytedance/UI-TARS-desktop", "bugs": "https://github.com/bytedance/UI-TARS-desktop/issues", "repository": {"type": "git", "url": "**************:bytedance/UI-TARS-desktop.git", "directory": "packages/agent-infra/mcp-http-server"}, "main": "./dist/index.js", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "exports": {".": {"import": "./dist/index.mjs", "require": "./dist/index.js", "types": "./dist/index.d.ts"}}, "files": ["dist"], "scripts": {"build": "shx rm -rf dist && rslib build", "prepare": "npm run build", "watch": "rslib build --watch"}, "dependencies": {"@modelcontextprotocol/sdk": "^1.11.2", "express": "^5.1.0"}, "devDependencies": {"@agent-infra/logger": "workspace:*", "@types/express": "^5.0.1", "cross-fetch": "4.1.0", "@rslib/core": "0.5.3", "tsx": "^4.19.3", "vitest": "^3.0.7", "@types/diff": "^5.0.9", "@types/minimatch": "^5.1.2", "@types/node": "^22", "typescript": "^5.7.3"}}