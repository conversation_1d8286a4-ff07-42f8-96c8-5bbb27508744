/**
 * Copyright (c) 2024-present Bytedance, Inc. and its affiliates.
 * SPDX-License-Identifier: MIT
 */
@import './common.less';

@player-spacing: 12px;
@player-vertical-spacing: 15px; // ~=12 * 1.2
@border-color: #979797;
@radius: 6px;
@player-control-bg: #666;
.player-container {
  width: fit-content;
  max-width: 100%;
  max-height: 100%;
  padding: @player-spacing 0;
  padding-bottom: 0;
  background: #434443DD;
  box-sizing: border-box;
  border: 1px solid @border-color;
  border-radius: @radius;
  line-height: 100%;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  overflow: hidden;

  .canvas-container {
    flex-grow: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    padding: 0 @player-spacing;
    canvas {
      max-width: 100%;
      max-height: 100%;
      box-sizing: border-box;
      display: block;
      margin: 0 auto;
    }
  }

  .player-timeline {
    width: 100%;
    height: 4px;
    background: @player-control-bg;
    position: relative;
    margin-top: -2px;

    .player-timeline-progress {
      transition-timing-function: linear;
      position: absolute;
      top: 0;
      left: 0;
      height: 4px;
      background: @primary-color;
    }
  }

  .player-tools {
    margin-top: @player-vertical-spacing;
    margin-bottom: @player-vertical-spacing;
    max-width: 100%;
    overflow: hidden;
    color: #FFF;
    font-size: 16px;
    box-sizing: border-box;
    display: flex;
    flex-direction: row;
    padding: 0 @player-spacing;
    justify-content: space-between;
    height: 40px;
    flex-shrink: 0;

    .player-control {
      flex-grow: 1;
      display: flex;
      flex-direction: row;
      align-items: left;
    }

    .status-icon {
      transition: .2s;
      width: 40px;
      height: 40px;
      margin-right: @player-spacing;
      background: @player-control-bg;
      border-radius: @radius;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-shrink: 0;
    }

    .status-text {
      flex-grow: 1;
      overflow: hidden;
      position: relative;
    }

    .title {
      font-weight: bold;
      height: 20px;
      line-height: 20px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
    }

    .subtitle {
      height: 20px;
      line-height: 20px;
      width: 100%;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      position: absolute;
      top: 18px;
      left: 0;
    }

    .player-tools-item {
      height: 100%;
      display: flex;
      flex-direction: column;
      justify-content: center;

      .ant-btn-variant-link {
        color: #FFF;
      }
    }
  }
}
