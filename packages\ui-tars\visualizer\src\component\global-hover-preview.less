/**
 * Copyright (c) 2024-present Bytedance, Inc. and its affiliates.
 * SPDX-License-Identifier: MIT
 */
@import './common.less';

@max-size: 400px;
.global-hover-preview {
  position: fixed;
  display: block;
  max-width: @max-size;
  max-height: @max-size;
  overflow: hidden;
  z-index: 10;
  text-align: center;
  border: 1px solid @border-color;
  box-sizing: border-box;
  background: @side-bg;
  box-shadow: 1px 1px 5px 0 rgba(0, 0, 0, 0.2);

  img {
    max-width: @max-size;
    max-height: @max-size;
    width: auto;
    height: auto;
  }
}
