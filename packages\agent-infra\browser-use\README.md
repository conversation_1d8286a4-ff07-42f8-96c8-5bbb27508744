# @agent-infra/browser-use

A browser automation and interaction library for AI agents, providing structured DOM access and browser control capabilities.

## Credits

Thanks to:

- The [browser-use](https://github.com/browser-use/browser-use) project which helps us operate the browser better.
- [alexchenzl](https://github.com/alexchenzl) for creating a great [nanobrowser](https://github.com/nanobrowser/nanobrowser) Chrome extension from which we got a lot of technical references when implementing browser in Electron
- The [puppeteer](https://github.com/puppeteer/puppeteer) project which helps us operate the browser better.
