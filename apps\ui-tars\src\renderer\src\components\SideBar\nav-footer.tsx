/**
 * Copyright (c) 2025 Bytedance, Inc. and its affiliates.
 * SPDX-License-Identifier: Apache-2.0
 */
import { Settings, Target } from 'lucide-react';

import {
  SidebarGroup,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from '@renderer/components/ui/sidebar';

interface NavSettingsProps {
  onSettingsClick: () => void;
  onCalibrationClick?: () => void;
}

export function NavSettings({
  onSettingsClick,
  onCalibrationClick,
}: NavSettingsProps) {
  return (
    <SidebarGroup>
      <SidebarMenu>
        {onCalibrationClick && (
          <SidebarMenuItem>
            <SidebarMenuButton
              className="h-12 font-medium"
              onClick={onCalibrationClick}
            >
              <Target strokeWidth={2} />
              <span>坐标校准</span>
            </SidebarMenuButton>
          </SidebarMenuItem>
        )}
        <SidebarMenuItem>
          <SidebarMenuButton
            className="h-12 font-medium"
            onClick={onSettingsClick}
          >
            <Settings strokeWidth={2} />
            <span>Settings</span>
          </SidebarMenuButton>
        </SidebarMenuItem>
      </SidebarMenu>
    </SidebarGroup>
  );
}
