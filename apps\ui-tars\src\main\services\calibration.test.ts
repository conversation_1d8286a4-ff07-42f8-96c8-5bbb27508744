/*
 * Copyright (c) 2025 Bytedance, Inc. and its affiliates.
 * SPDX-License-Identifier: Apache-2.0
 */
import { describe, it, expect, beforeEach } from 'vitest';
import { calibrationService } from './calibration';

describe('CalibrationService', () => {
  beforeEach(() => {
    calibrationService.clearCalibrationData();
  });

  it('should start and stop calibration', async () => {
    expect(calibrationService.isCalibrationActive()).toBe(false);
    
    await calibrationService.startCalibration();
    expect(calibrationService.isCalibrationActive()).toBe(true);
    
    await calibrationService.stopCalibration();
    expect(calibrationService.isCalibrationActive()).toBe(false);
  });

  it('should save and retrieve calibration data', async () => {
    const testData = {
      offsetX: 10,
      offsetY: 15,
      points: [
        {
          pointId: 'center',
          expectedX: 500,
          expectedY: 300,
          actualX: 510,
          actualY: 315,
          offsetX: 10,
          offsetY: 15,
        },
      ],
      timestamp: Date.now(),
    };

    await calibrationService.saveCalibrationData(testData);
    
    const retrieved = calibrationService.getCalibrationData();
    expect(retrieved).toEqual(testData);
    expect(calibrationService.isCalibrationEnabled()).toBe(true);
  });

  it('should apply calibrated coordinates', () => {
    const testData = {
      offsetX: 10,
      offsetY: 15,
      points: [],
      timestamp: Date.now(),
    };

    calibrationService.saveCalibrationData(testData);
    
    const result = calibrationService.applyCalibratedCoordinates(100, 200);
    expect(result).toEqual({ x: 90, y: 185 });
  });

  it('should return original coordinates when calibration is disabled', () => {
    calibrationService.enableCalibration(false);
    
    const result = calibrationService.applyCalibratedCoordinates(100, 200);
    expect(result).toEqual({ x: 100, y: 200 });
  });

  it('should get calibration stats', async () => {
    const testData = {
      offsetX: 10,
      offsetY: 15,
      points: [
        {
          pointId: 'center',
          expectedX: 500,
          expectedY: 300,
          actualX: 510,
          actualY: 315,
          offsetX: 10,
          offsetY: 15,
        },
        {
          pointId: 'top',
          expectedX: 500,
          expectedY: 100,
          actualX: 505,
          actualY: 110,
          offsetX: 5,
          offsetY: 10,
        },
      ],
      timestamp: Date.now(),
    };

    await calibrationService.saveCalibrationData(testData);
    
    const stats = calibrationService.getCalibrationStats();
    expect(stats).toBeDefined();
    expect(stats.pointsCount).toBe(2);
    expect(stats.avgOffset.x).toBe(7.5); // (10 + 5) / 2
    expect(stats.avgOffset.y).toBe(12.5); // (15 + 10) / 2
    expect(stats.maxOffset.x).toBe(10);
    expect(stats.maxOffset.y).toBe(15);
  });
});
