/*
 * Copyright (c) 2025 Bytedance, Inc. and its affiliates.
 * SPDX-License-Identifier: Apache-2.0
 */
import React, { useState, useEffect } from 'react';
import { Button } from '../ui/button';

interface CalibrationPoint {
  id: string;
  x: number;
  y: number;
  label: string;
  clicked: boolean;
}

interface CalibrationOverlayProps {
  isVisible: boolean;
  onClose: () => void;
  onCalibrationComplete: (calibrationData: any) => void;
}

export const CalibrationOverlay: React.FC<CalibrationOverlayProps> = ({
  isVisible,
  onClose,
  onCalibrationComplete,
}) => {
  const [calibrationPoints, setCalibrationPoints] = useState<
    CalibrationPoint[]
  >([]);
  const [currentStep, setCurrentStep] = useState(0);
  const [isCalibrating, setIsCalibrating] = useState(false);
  const [calibrationData, setCalibrationData] = useState<any[]>([]);

  useEffect(() => {
    if (isVisible) {
      initializeCalibrationPoints();
    }
  }, [isVisible]);

  const initializeCalibrationPoints = () => {
    const screenWidth = window.innerWidth;
    const screenHeight = window.innerHeight;

    const points: CalibrationPoint[] = [
      {
        id: 'center',
        x: screenWidth / 2,
        y: screenHeight / 2,
        label: '中央',
        clicked: false,
      },
      {
        id: 'top',
        x: screenWidth / 2,
        y: screenHeight * 0.2,
        label: '上方',
        clicked: false,
      },
      {
        id: 'bottom',
        x: screenWidth / 2,
        y: screenHeight * 0.8,
        label: '下方',
        clicked: false,
      },
      {
        id: 'left',
        x: screenWidth * 0.2,
        y: screenHeight / 2,
        label: '左侧',
        clicked: false,
      },
      {
        id: 'right',
        x: screenWidth * 0.8,
        y: screenHeight / 2,
        label: '右侧',
        clicked: false,
      },
    ];

    setCalibrationPoints(points);
    setCurrentStep(0);
    setCalibrationData([]);
  };

  const startCalibration = async () => {
    setIsCalibrating(true);
    setCurrentStep(0);

    // 通知主进程开始校准
    await window.electronAPI.calibration.start();

    // 开始监听点击事件
    window.electronAPI.calibration.onCalibrationClick(
      (actualX: number, actualY: number) => {
        handleCalibrationClick(actualX, actualY);
      },
    );
  };

  const handleCalibrationClick = (actualX: number, actualY: number) => {
    if (currentStep >= calibrationPoints.length) return;

    const currentPoint = calibrationPoints[currentStep];
    const expectedX = currentPoint.x;
    const expectedY = currentPoint.y;

    // 记录校准数据
    const calibrationEntry = {
      pointId: currentPoint.id,
      expectedX,
      expectedY,
      actualX,
      actualY,
      offsetX: actualX - expectedX,
      offsetY: actualY - expectedY,
    };

    setCalibrationData((prev) => [...prev, calibrationEntry]);

    // 更新点击状态
    setCalibrationPoints((prev) =>
      prev.map((point, index) =>
        index === currentStep ? { ...point, clicked: true } : point,
      ),
    );

    // 移动到下一个点
    const nextStep = currentStep + 1;
    setCurrentStep(nextStep);

    if (nextStep >= calibrationPoints.length) {
      // 校准完成
      completeCalibration([...calibrationData, calibrationEntry]);
    }
  };

  const completeCalibration = async (finalCalibrationData: any[]) => {
    setIsCalibrating(false);

    // 计算平均偏差
    const avgOffsetX =
      finalCalibrationData.reduce((sum, data) => sum + data.offsetX, 0) /
      finalCalibrationData.length;
    const avgOffsetY =
      finalCalibrationData.reduce((sum, data) => sum + data.offsetY, 0) /
      finalCalibrationData.length;

    const calibrationResult = {
      offsetX: avgOffsetX,
      offsetY: avgOffsetY,
      points: finalCalibrationData,
      timestamp: Date.now(),
    };

    // 保存校准数据
    await window.electronAPI.calibration.save(calibrationResult);

    onCalibrationComplete(calibrationResult);
    onClose();
  };

  if (!isVisible) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center">
      <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
        <h2 className="text-xl font-bold mb-4">坐标校准</h2>

        {!isCalibrating ? (
          <div>
            <p className="mb-4">
              此功能将在屏幕上显示5个校准点，请让您的本地模型依次点击这些点来校准坐标偏差。
            </p>
            <div className="flex gap-2">
              <Button onClick={startCalibration} className="flex-1">
                开始校准
              </Button>
              <Button onClick={onClose} variant="outline" className="flex-1">
                取消
              </Button>
            </div>
          </div>
        ) : (
          <div>
            <p className="mb-4">
              请让模型点击{' '}
              <strong>{calibrationPoints[currentStep]?.label}</strong>{' '}
              位置的红色圆点
            </p>
            <div className="mb-4">
              进度: {currentStep + 1} / {calibrationPoints.length}
            </div>
            <Button
              onClick={() => {
                setIsCalibrating(false);
                onClose();
              }}
              variant="outline"
              className="w-full"
            >
              停止校准
            </Button>
          </div>
        )}
      </div>

      {/* 校准点显示 */}
      {isCalibrating && (
        <div className="fixed inset-0 pointer-events-none">
          {calibrationPoints.map((point, index) => (
            <div
              key={point.id}
              className={`absolute w-8 h-8 rounded-full border-4 flex items-center justify-center text-white font-bold ${
                index === currentStep
                  ? 'bg-red-500 border-red-700 animate-pulse'
                  : point.clicked
                    ? 'bg-green-500 border-green-700'
                    : 'bg-gray-400 border-gray-600'
              }`}
              style={{
                left: point.x - 16,
                top: point.y - 16,
              }}
            >
              {index + 1}
            </div>
          ))}
        </div>
      )}
    </div>
  );
};
