# UI-TARS 坐标校准功能使用指南

## 功能概述

UI-TARS 坐标校准功能旨在解决本地模型点击坐标不准确的问题。通过在屏幕上显示5个校准点（上、下、左、右、中央），让本地模型依次点击这些点，系统会计算模型实际点击位置与目标位置的偏差，并在后续操作中自动应用校准。

## 功能特点

- **5点校准**：在屏幕的上、下、左、右、中央位置显示校准点
- **自动偏差计算**：计算平均偏差并应用到后续操作
- **实时校准**：校准数据会立即应用到新的点击操作
- **持久化存储**：校准数据保存到本地，重启应用后仍然有效
- **可开关控制**：可以随时启用或禁用校准功能

## 使用步骤

### 1. 启动校准

1. 在 UI-TARS 主界面左侧边栏底部，点击 **"坐标校准"** 按钮
2. 在弹出的校准对话框中，点击 **"开始校准"** 按钮
3. 屏幕上会显示5个校准点，当前需要点击的点会以红色高亮显示

### 2. 执行校准

1. 让您的本地模型依次点击屏幕上显示的校准点
2. 校准顺序：中央 → 上方 → 下方 → 左侧 → 右侧
3. 每次点击后，系统会自动记录实际点击位置与目标位置的偏差
4. 已点击的点会变为绿色，当前目标点为红色闪烁

### 3. 完成校准

1. 当所有5个点都被点击后，系统会自动计算平均偏差
2. 校准数据会保存到本地存储
3. 校准功能会自动启用，后续的点击操作都会应用校准偏差

## 校准数据管理

### 查看校准状态

校准完成后，您可以通过以下方式查看校准状态：
- 校准数据会显示平均偏差值（X轴和Y轴）
- 系统会记录校准时间戳
- 可以查看每个校准点的详细偏差数据

### 重新校准

如果发现校准效果不理想，可以：
1. 再次点击 **"坐标校准"** 按钮
2. 重新执行校准流程
3. 新的校准数据会覆盖旧数据

### 禁用校准

如果需要临时禁用校准功能：
1. 在设置中可以找到校准开关
2. 禁用后，系统会使用原始坐标，不应用任何偏差修正

## 技术原理

### 坐标转换

1. **相对坐标**：模型输出的坐标是相对于屏幕尺寸的比例值（0-1范围）
2. **绝对坐标**：系统将相对坐标转换为屏幕上的绝对像素坐标
3. **校准应用**：在绝对坐标基础上减去校准偏差，得到修正后的坐标

### 偏差计算

```
偏差X = 实际点击X - 目标点击X
偏差Y = 实际点击Y - 目标点击Y
平均偏差X = Σ(偏差X) / 校准点数量
平均偏差Y = Σ(偏差Y) / 校准点数量
```

### 校准应用

```
修正后X = 原始X - 平均偏差X
修正后Y = 原始Y - 平均偏差Y
```

## 注意事项

1. **屏幕分辨率**：校准数据与屏幕分辨率相关，更换显示器或修改分辨率后需要重新校准
2. **模型一致性**：确保校准时使用的模型与实际使用的模型一致
3. **环境稳定**：校准时确保系统环境稳定，避免其他程序干扰
4. **精确点击**：校准时尽量让模型精确点击校准点的中心位置

## 故障排除

### 校准点击无响应

- 检查模型是否正确连接
- 确认模型输出的坐标格式正确
- 查看控制台日志是否有错误信息

### 校准效果不佳

- 重新执行校准流程
- 检查校准时的点击是否准确
- 考虑调整模型参数或使用不同的模型

### 校准数据丢失

- 校准数据保存在本地存储中
- 如果数据丢失，需要重新执行校准
- 可以在开发者工具中检查存储状态

## API 参考

### 主要接口

```typescript
// 开始校准
await window.electronAPI.calibration.start();

// 保存校准数据
await window.electronAPI.calibration.save(calibrationData);

// 获取校准数据
const data = await window.electronAPI.calibration.get();

// 启用/禁用校准
await window.electronAPI.calibration.setEnabled(true);

// 清除校准数据
await window.electronAPI.calibration.clear();
```

### 校准数据结构

```typescript
interface CalibrationData {
  offsetX: number;        // X轴平均偏差
  offsetY: number;        // Y轴平均偏差
  points: Array<{         // 校准点详细数据
    pointId: string;      // 点ID（center, top, bottom, left, right）
    expectedX: number;    // 期望X坐标
    expectedY: number;    // 期望Y坐标
    actualX: number;      // 实际X坐标
    actualY: number;      // 实际Y坐标
    offsetX: number;      // X偏差
    offsetY: number;      // Y偏差
  }>;
  timestamp: number;      // 校准时间戳
}
```

## 更新日志

### v1.0.0
- 初始版本发布
- 支持5点校准
- 自动偏差计算和应用
- 本地数据持久化
