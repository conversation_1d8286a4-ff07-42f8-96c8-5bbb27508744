/*
 * Copyright (c) 2025 Bytedance, Inc. and its affiliates.
 * SPDX-License-Identifier: Apache-2.0
 */
import { Key, keyboard } from '@computer-use/nut-js';
import {
  type ScreenshotOutput,
  type ExecuteParams,
  type ExecuteOutput,
} from '@ui-tars/sdk/core';
import { NutJSOperator } from '@ui-tars/operator-nut-js';
import { clipboard } from 'electron';
import { desktopCapturer } from 'electron';

import * as env from '@main/env';
import { logger } from '@main/logger';
import { sleep } from '@ui-tars/shared/utils';
import { getScreenSize } from '@main/utils/screen';
import { calibrationService } from '@main/services/calibration';

export class NutJSElectronOperator extends NutJSOperator {
  static MANUAL = {
    ACTION_SPACES: [
      `click(start_box='[x1, y1, x2, y2]')`,
      `left_double(start_box='[x1, y1, x2, y2]')`,
      `right_single(start_box='[x1, y1, x2, y2]')`,
      `drag(start_box='[x1, y1, x2, y2]', end_box='[x3, y3, x4, y4]')`,
      `hotkey(key='')`,
      `type(content='') #If you want to submit your input, use "\\n" at the end of \`content\`.`,
      `scroll(start_box='[x1, y1, x2, y2]', direction='down or up or right or left')`,
      `wait() #Sleep for 5s and take a screenshot to check for any changes.`,
      `finished()`,
      `call_user() # Submit the task and call the user when the task is unsolvable, or when you need the user's help.`,
    ],
  };

  public async screenshot(): Promise<ScreenshotOutput> {
    const {
      physicalSize,
      logicalSize,
      scaleFactor,
      id: primaryDisplayId,
    } = getScreenSize(); // Logical = Physical / scaleX

    logger.info(
      '[screenshot] [primaryDisplay]',
      'logicalSize:',
      logicalSize,
      'scaleFactor:',
      scaleFactor,
    );

    const sources = await desktopCapturer.getSources({
      types: ['screen'],
      thumbnailSize: {
        width: Math.round(logicalSize.width),
        height: Math.round(logicalSize.height),
      },
    });
    const primarySource =
      sources.find(
        (source) => source.display_id === primaryDisplayId.toString(),
      ) || sources[0];

    if (!primarySource) {
      logger.error('[screenshot] Primary display source not found', {
        primaryDisplayId,
        availableSources: sources.map((s) => s.display_id),
      });
      // fallback to default screenshot
      return await super.screenshot();
    }

    const screenshot = primarySource.thumbnail;

    const resized = screenshot.resize({
      width: physicalSize.width,
      height: physicalSize.height,
    });

    return {
      base64: resized.toJPEG(75).toString('base64'),
      scaleFactor,
    };
  }

  async execute(params: ExecuteParams): Promise<ExecuteOutput> {
    const { action_type, action_inputs } = params.parsedPrediction;

    // 如果是点击类操作，记录实际点击位置用于校准
    if (
      [
        'click',
        'left_click',
        'left_single',
        'left_double',
        'double_click',
        'right_click',
        'right_single',
      ].includes(action_type)
    ) {
      const startBoxStr = action_inputs?.start_box || '';
      if (startBoxStr && calibrationService.isCalibrationActive()) {
        // 解析目标坐标
        const { parseBoxToScreenCoords } = await import('@ui-tars/sdk/core');
        const { x, y } = parseBoxToScreenCoords({
          boxStr: startBoxStr,
          screenWidth: params.screenWidth,
          screenHeight: params.screenHeight,
        });

        if (x !== null && y !== null) {
          // 记录实际点击位置
          calibrationService.recordActualClick(x, y);
        }
      }
    }

    if (action_type === 'type' && env.isWindows && action_inputs?.content) {
      const content = action_inputs.content?.trim();

      logger.info('[device] type', content);
      const stripContent = content.replace(/\\n$/, '').replace(/\n$/, '');
      const originalClipboard = clipboard.readText();
      clipboard.writeText(stripContent);
      await keyboard.pressKey(Key.LeftControl, Key.V);
      await sleep(50);
      await keyboard.releaseKey(Key.LeftControl, Key.V);
      await sleep(50);
      clipboard.writeText(originalClipboard);
    } else {
      // 应用校准到坐标
      const calibratedParams = this.applyCalibratedParams(params);
      return await super.execute(calibratedParams);
    }
  }

  /**
   * 应用校准到执行参数
   */
  private applyCalibratedParams(params: ExecuteParams): ExecuteParams {
    const { action_type, action_inputs } = params.parsedPrediction;

    // 只对点击类操作应用校准
    if (
      ![
        'click',
        'left_click',
        'left_single',
        'left_double',
        'double_click',
        'right_click',
        'right_single',
        'drag',
        'left_click_drag',
        'select',
      ].includes(action_type)
    ) {
      return params;
    }

    if (!calibrationService.isCalibrationEnabled()) {
      return params;
    }

    const startBoxStr = action_inputs?.start_box || '';
    if (!startBoxStr) {
      return params;
    }

    try {
      // 解析原始坐标
      const { parseBoxToScreenCoords } = require('@ui-tars/sdk/core');
      const { x: originalX, y: originalY } = parseBoxToScreenCoords({
        boxStr: startBoxStr,
        screenWidth: params.screenWidth,
        screenHeight: params.screenHeight,
      });

      if (originalX === null || originalY === null) {
        return params;
      }

      // 应用校准
      const { x: calibratedX, y: calibratedY } =
        calibrationService.applyCalibratedCoordinates(originalX, originalY);

      // 转换回相对坐标
      const relativeX = calibratedX / params.screenWidth;
      const relativeY = calibratedY / params.screenHeight;

      // 构建新的box字符串
      const newStartBox = `[${relativeX},${relativeY},${relativeX},${relativeY}]`;

      // 创建新的参数对象
      const calibratedParams = {
        ...params,
        parsedPrediction: {
          ...params.parsedPrediction,
          action_inputs: {
            ...action_inputs,
            start_box: newStartBox,
          },
        },
      };

      logger.info('[NutJSElectronOperator] Applied calibration', {
        original: { x: originalX, y: originalY },
        calibrated: { x: calibratedX, y: calibratedY },
        originalBox: startBoxStr,
        calibratedBox: newStartBox,
      });

      return calibratedParams;
    } catch (error) {
      logger.error(
        '[NutJSElectronOperator] Error applying calibration:',
        error,
      );
      return params;
    }
  }
}
