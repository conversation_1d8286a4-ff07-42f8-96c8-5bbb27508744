{"name": "@agent-infra/logger", "description": "An tiny isomorphic logger", "version": "0.0.1", "main": "dist/index.js", "module": "dist/index.mjs", "types": "dist/index.d.ts", "exports": {".": {"import": "./dist/index.mjs", "require": "./dist/index.js", "types": "./dist/index.d.ts"}}, "files": ["dist"], "scripts": {"dev": "rslib build --watch", "build": "rslib build", "prepare": "npm run build", "prepublishOnly": "pnpm run build"}, "devDependencies": {"@types/node": "20.14.8", "typescript": "4.9.4", "@rslib/core": "0.5.3"}}