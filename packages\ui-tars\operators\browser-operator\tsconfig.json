{"compilerOptions": {"target": "esnext", "module": "esnext", "moduleResolution": "<PERSON><PERSON><PERSON>", "declaration": true, "declarationMap": true, "sourceMap": true, "outDir": "./dist", "strict": true, "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "isolatedModules": true, "skipLibCheck": true}, "include": ["src/**/*"], "exclude": ["node_modules", "dist"]}