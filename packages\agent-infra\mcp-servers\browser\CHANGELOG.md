# @agent-infra/mcp-server-browser

## 1.1.1

### Patch Changes

- a40b3c1: feat: add mcp http server
- 2598ea6: feat: native support sse and mcp serving by browser mcp
- 82fe970: fix: hang up
- 1e12e91: fix: schema empty not right
- feat: support high performance sse and http server
- Updated dependencies [a40b3c1]
- Updated dependencies [82fe970]
- Updated dependencies
  - mcp-http-server@1.1.1

## 1.1.1-beta.3

### Patch Changes

- fix: hang up
- Updated dependencies
  - mcp-http-server@1.1.1-beta.3

## 1.1.1-beta.2

### Patch Changes

- feat: add mcp http server
- Updated dependencies
  - @agent-infra/mcp-http-server@1.1.1-beta.2

## 1.1.1-beta.1

### Patch Changes

- fix: schema empty not right

## 1.1.1-beta.0

### Patch Changes

- feat: native support sse and mcp serving by browser mcp

## 1.1.0

### Minor Changes

- feat(agent-tars): provider mcp servers

### Patch Changes

- 9089c63: fix: version
- 9089c63: chore: add dumpio
- 9545e06: chore: mcp-browser args alignment playwright-mcp
- 0bdfa56: feat: support cdp
- 9089c63: fix: navigate adblock timeout
- 2d804f7: chore: typo
- 030f31d: feat: add mcp browser proxy-bypass-list
- 4860d9e: feat: new version release
- 9089c63: feat: launchOptions add args
- 9089c63: feat: auth parse proxy url username and password
- 9089c63: fix: adblock
- 9089c63: chore: update @ghostery/adblocker-puppeteer
- 9089c63: refactor: mcp servers with high-level api
- 9089c63: feat: add page proxy authentication
- eaf5d3b: fix: display
- 9089c63: chore: error catch

## 1.0.1-beta.15

### Patch Changes

- feat: new version release

## 1.0.1-beta.14

### Patch Changes

- feat: support cdp

## 1.0.1-beta.13

### Patch Changes

- fix: display

## 1.0.1-beta.12

### Patch Changes

- chore: mcp-browser args alignment playwright-mcp

## 1.0.1-beta.11

### Patch Changes

- chore: typo

## 1.0.1-beta.10

### Patch Changes

- feat: add mcp browser proxy-bypass-list

## 1.0.1-beta.9

### Patch Changes

- feat: auth parse proxy url username and password

## 1.0.1-beta.8

### Patch Changes

- fix: version

## 1.0.1-beta.7

### Patch Changes

- refactor: mcp servers with high-level api

## 0.0.3-beta.6

### Patch Changes

- chore: error catch

## 0.0.3-beta.5

### Patch Changes

- chore: update @ghostery/adblocker-puppeteer

## 0.0.3-beta.4

### Patch Changes

- fix: adblock

## 0.0.3-beta.3

### Patch Changes

- fix: navigate adblock timeout

## 0.0.3-beta.2

### Patch Changes

- feat: add page proxy authentication

## 0.0.3-beta.1

### Patch Changes

- chore: add dumpio

## 0.0.3-beta.0

### Patch Changes

- feat: launchOptions add args

## 0.0.2

### Patch Changes

- fix: browser index click
