/*
 * Copyright (c) 2025 Bytedance, Inc. and its affiliates.
 * SPDX-License-Identifier: Apache-2.0
 */
import { ipcMain } from 'electron';
import { calibrationService } from '@main/services/calibration';
import { logger } from '@main/logger';

export function registerCalibrationRoutes() {
  // 开始校准
  ipcMain.handle('calibration:start', async () => {
    try {
      await calibrationService.startCalibration();
      return { success: true };
    } catch (error) {
      logger.error('[IPC] calibration:start error:', error);
      return { success: false, error: error.message };
    }
  });

  // 停止校准
  ipcMain.handle('calibration:stop', async () => {
    try {
      await calibrationService.stopCalibration();
      return { success: true };
    } catch (error) {
      logger.error('[IPC] calibration:stop error:', error);
      return { success: false, error: error.message };
    }
  });

  // 保存校准数据
  ipcMain.handle('calibration:save', async (event, calibrationData) => {
    try {
      await calibrationService.saveCalibrationData(calibrationData);
      return { success: true };
    } catch (error) {
      logger.error('[IPC] calibration:save error:', error);
      return { success: false, error: error.message };
    }
  });

  // 获取校准数据
  ipcMain.handle('calibration:get', async () => {
    try {
      const data = calibrationService.getCalibrationData();
      return { success: true, data };
    } catch (error) {
      logger.error('[IPC] calibration:get error:', error);
      return { success: false, error: error.message };
    }
  });

  // 检查校准是否启用
  ipcMain.handle('calibration:isEnabled', async () => {
    try {
      const enabled = calibrationService.isCalibrationEnabled();
      return { success: true, enabled };
    } catch (error) {
      logger.error('[IPC] calibration:isEnabled error:', error);
      return { success: false, error: error.message };
    }
  });

  // 启用/禁用校准
  ipcMain.handle('calibration:setEnabled', async (event, enabled: boolean) => {
    try {
      calibrationService.enableCalibration(enabled);
      return { success: true };
    } catch (error) {
      logger.error('[IPC] calibration:setEnabled error:', error);
      return { success: false, error: error.message };
    }
  });

  // 清除校准数据
  ipcMain.handle('calibration:clear', async () => {
    try {
      calibrationService.clearCalibrationData();
      return { success: true };
    } catch (error) {
      logger.error('[IPC] calibration:clear error:', error);
      return { success: false, error: error.message };
    }
  });

  // 获取校准统计信息
  ipcMain.handle('calibration:getStats', async () => {
    try {
      const stats = calibrationService.getCalibrationStats();
      return { success: true, stats };
    } catch (error) {
      logger.error('[IPC] calibration:getStats error:', error);
      return { success: false, error: error.message };
    }
  });

  // 检查是否正在校准
  ipcMain.handle('calibration:isActive', async () => {
    try {
      const active = calibrationService.isCalibrationActive();
      return { success: true, active };
    } catch (error) {
      logger.error('[IPC] calibration:isActive error:', error);
      return { success: false, error: error.message };
    }
  });

  logger.info('[IPC] Calibration routes registered');
}
