{"name": "@agent-infra/mcp-server-commands", "version": "1.1.1", "description": "An MCP server to run arbitrary commands", "type": "module", "main": "./dist/server.cjs", "module": "./dist/server.js", "types": "./dist/server.d.ts", "bin": {"mcp-server-commands": "./dist/index.cjs"}, "homepage": "https://github.com/bytedance/UI-TARS-desktop", "bugs": "https://github.com/bytedance/UI-TARS-desktop/issues", "repository": {"type": "git", "url": "**************:bytedance/UI-TARS-desktop.git", "directory": "packages/agent-infra/mcp-servers/commands"}, "files": ["dist"], "scripts": {"clean": "shx rm -rf build", "build": "shx rm -rf build && rslib build && shx chmod +x dist/*.{js,cjs}", "prepare": "npm run build", "watch": "npm run build && rslib build --watch", "dev": "npx -y @modelcontextprotocol/inspector tsx src/index.ts", "inspector": "npx @modelcontextprotocol/inspector build/index.js", "test": "vitest run", "test:watch": "vitest --watch", "test:integration": "vitest tests/integration"}, "dependencies": {"mcp-http-server": "workspace:*", "@modelcontextprotocol/sdk": "^1.11.2"}, "devDependencies": {"commander": "^13.1.0", "tsx": "^4.19.3", "@rslib/core": "0.5.3", "zod": "^3.23.8", "shx": "^0.3.4", "vitest": "^3.0.7", "@types/node": "^20.11.24", "typescript": "^5.7.3"}}