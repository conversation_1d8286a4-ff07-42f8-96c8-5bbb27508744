# Ollama 快速开始指南

本指南将帮助您快速在UI-TARS桌面应用中设置和使用Ollama本地模型。

## 🚀 快速开始

### 1. 确保Ollama正在运行

确保您已经安装了Ollama并且您的 `qwen2.5vl:3b` 模型正在运行：

```bash
# 检查Ollama是否运行
curl http://localhost:11434/api/tags

# 如果没有运行，启动Ollama
ollama serve

# 验证您的模型
ollama list
```

### 2. 安装项目依赖

```bash
# 安装依赖
pnpm install

# 或者如果您使用npm
npm install
```

### 3. 运行应用程序

运行Agent TARS应用（推荐，因为它包含完整的AI代理功能）：

```bash
pnpm run dev:agent-tars
```

或者运行UI TARS应用：

```bash
pnpm run dev:ui-tars
```

### 4. 配置Ollama

1. 启动应用后，进入 **设置** (Settings)
2. 在 **模型设置** (Model Settings) 部分配置：
   - **Provider**: 选择 "Ollama"
   - **Model**: 输入 "qwen2.5vl:3b"
   - **API Key**: 输入 "ollama"
   - **Endpoint**: 输入 "http://localhost:11434/v1"

### 5. 测试连接

保存设置后，应用程序应该能够连接到您的本地Ollama实例。

## ✅ 集成完成

我已经为您的UI-TARS项目完成了以下Ollama集成：

### 🔧 后端集成（Agent TARS）

1. **OllamaProvider类** (`apps/agent-tars/src/main/llmProvider/providers/OllamaProvider.ts`)
   - 完整的Ollama提供商实现
   - 支持文本生成、工具调用和流式响应
   - 使用OpenAI兼容的API接口

2. **ProviderFactory更新** (`apps/agent-tars/src/main/llmProvider/ProviderFactory.ts`)
   - 添加了Ollama到可用提供商列表
   - 支持通过模型名称自动检测Ollama（如'qwen', 'llama'等前缀）

3. **配置支持** (`packages/agent-infra/shared/src/agent-tars-types/setting.ts`)
   - 添加了`ModelProvider.OLLAMA`枚举值

### 🎨 前端集成（Agent TARS UI）

1. **UI组件更新** (`apps/agent-tars/src/renderer/src/components/LeftSidebar/Settings/modelUtils.tsx`)
   - 添加了Ollama图标支持
   - 提供了常用Ollama模型的预设选项（包括您的qwen2.5vl:3b）

2. **模型选项**
   - qwen2.5vl:3b (您当前的模型)
   - qwen2.5vl:7b
   - qwen2.5vl:32b
   - llama3.2-vision:11b
   - llama3.2-vision:90b
   - minicpm-v:8b

### 📁 文档和配置

1. **环境变量示例** (`apps/agent-tars/ollama-config.example.env`)
2. **详细集成指南** (`docs/ollama-integration.md`)
3. **LLM Provider README更新** - 添加了Ollama配置说明

## 🔍 支持的功能

- ✅ 文本生成
- ✅ 流式响应  
- ✅ 工具/函数调用
- ✅ 视觉语言模型支持
- ✅ 本地推理（数据隐私）
- ✅ 离线使用

## 📝 环境变量配置

您可以设置以下环境变量：

```env
OLLAMA_API_KEY=ollama
OLLAMA_BASE_URL=http://localhost:11434/v1
OLLAMA_DEFAULT_MODEL=qwen2.5vl:3b
```

## 🛠️ 故障排除

如果遇到问题：

1. **检查Ollama服务状态**:
   ```bash
   curl http://localhost:11434/api/tags
   ```

2. **验证模型可用**:
   ```bash
   ollama list
   ```

3. **测试模型响应**:
   ```bash
   ollama run qwen2.5vl:3b "Hello, how are you?"
   ```

4. **检查应用日志**：
   - 查看控制台输出是否有连接错误
   - 确保端口11434未被其他应用占用

## 🎉 使用技巧

1. **模型选择**：
   - `qwen2.5vl:3b` - 快速响应，适合开发测试
   - `qwen2.5vl:7b` - 更好的性能，平衡速度和质量
   - `qwen2.5vl:32b` - 最佳质量，需要强大硬件

2. **性能优化**：
   - 确保Ollama在GPU上运行（如果有）
   - 关闭不必要的应用程序释放内存
   - 考虑使用SSD以提高模型加载速度

现在您可以享受使用本地AI模型的强大功能，同时保护您的数据隐私！ 