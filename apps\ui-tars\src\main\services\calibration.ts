/*
 * Copyright (c) 2025 Bytedance, Inc. and its affiliates.
 * SPDX-License-Identifier: Apache-2.0
 */
import { BrowserWindow } from 'electron';
import { logger } from '@main/logger';
import Store from 'electron-store';

interface CalibrationData {
  offsetX: number;
  offsetY: number;
  points: Array<{
    pointId: string;
    expectedX: number;
    expectedY: number;
    actualX: number;
    actualY: number;
    offsetX: number;
    offsetY: number;
  }>;
  timestamp: number;
}

interface CalibrationPoint {
  x: number;
  y: number;
  label: string;
}

class CalibrationService {
  private store: Store;
  private isCalibrating = false;
  private currentCalibrationData: CalibrationData | null = null;
  private mainWindow: BrowserWindow | null = null;

  constructor() {
    this.store = new Store({
      name: 'calibration-data',
      defaults: {
        calibrationData: null,
        isCalibrationEnabled: false,
      },
    });
  }

  setMainWindow(window: BrowserWindow) {
    this.mainWindow = window;
  }

  async startCalibration(): Promise<void> {
    logger.info('[CalibrationService] Starting calibration');
    this.isCalibrating = true;
    this.currentCalibrationData = null;
  }

  async stopCalibration(): Promise<void> {
    logger.info('[CalibrationService] Stopping calibration');
    this.isCalibrating = false;
    this.currentCalibrationData = null;
  }

  isCalibrationActive(): boolean {
    return this.isCalibrating;
  }

  async saveCalibrationData(data: CalibrationData): Promise<void> {
    logger.info('[CalibrationService] Saving calibration data', {
      offsetX: data.offsetX,
      offsetY: data.offsetY,
      pointsCount: data.points.length,
    });

    this.store.set('calibrationData', data);
    this.store.set('isCalibrationEnabled', true);
    this.currentCalibrationData = data;
    this.isCalibrating = false;
  }

  getCalibrationData(): CalibrationData | null {
    return this.store.get('calibrationData') as CalibrationData | null;
  }

  isCalibrationEnabled(): boolean {
    return this.store.get('isCalibrationEnabled') as boolean;
  }

  enableCalibration(enabled: boolean): void {
    this.store.set('isCalibrationEnabled', enabled);
    logger.info('[CalibrationService] Calibration enabled:', enabled);
  }

  /**
   * 应用校准偏差到坐标
   */
  applyCalibratedCoordinates(x: number, y: number): { x: number; y: number } {
    if (!this.isCalibrationEnabled()) {
      return { x, y };
    }

    const calibrationData = this.getCalibrationData();
    if (!calibrationData) {
      return { x, y };
    }

    const calibratedX = x - calibrationData.offsetX;
    const calibratedY = y - calibrationData.offsetY;

    logger.info('[CalibrationService] Applied calibration', {
      original: { x, y },
      offset: { x: calibrationData.offsetX, y: calibrationData.offsetY },
      calibrated: { x: calibratedX, y: calibratedY },
    });

    return { x: calibratedX, y: calibratedY };
  }

  /**
   * 记录实际点击位置（用于校准过程中）
   */
  recordActualClick(x: number, y: number): void {
    if (!this.isCalibrating) {
      return;
    }

    logger.info('[CalibrationService] Recording actual click', { x, y });

    // 通知渲染进程
    if (this.mainWindow) {
      this.mainWindow.webContents.send('calibration-click', x, y);
    }
  }

  /**
   * 清除校准数据
   */
  clearCalibrationData(): void {
    this.store.delete('calibrationData');
    this.store.set('isCalibrationEnabled', false);
    this.currentCalibrationData = null;
    logger.info('[CalibrationService] Calibration data cleared');
  }

  /**
   * 获取校准统计信息
   */
  getCalibrationStats(): any {
    const data = this.getCalibrationData();
    if (!data) {
      return null;
    }

    const points = data.points;
    const avgOffsetX = points.reduce((sum, p) => sum + p.offsetX, 0) / points.length;
    const avgOffsetY = points.reduce((sum, p) => sum + p.offsetY, 0) / points.length;
    
    const maxOffsetX = Math.max(...points.map(p => Math.abs(p.offsetX)));
    const maxOffsetY = Math.max(...points.map(p => Math.abs(p.offsetY)));

    return {
      pointsCount: points.length,
      avgOffset: { x: avgOffsetX, y: avgOffsetY },
      maxOffset: { x: maxOffsetX, y: maxOffsetY },
      timestamp: data.timestamp,
      isEnabled: this.isCalibrationEnabled(),
    };
  }
}

export const calibrationService = new CalibrationService();
