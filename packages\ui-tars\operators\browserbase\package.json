{"name": "@ui-tars/operator-browserbase", "version": "1.2.1", "description": "Operator Browserbase SDK for UI-TARS", "repository": {"type": "git", "url": "https://github.com/bytedance/UI-TARS-desktop"}, "bugs": {"url": "https://github.com/bytedance/UI-TARS-desktop/issues"}, "keywords": ["AI", "Core", "SDK", "Operator", "UI-TARS"], "main": "./dist/index.js", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "scripts": {"dev": "rslib build --watch", "build": "rslib build", "build:watch": "rslib build --watch", "prepare": "npm run build", "test": "vitest"}, "license": "Apache-2.0", "files": ["dist"], "publishConfig": {"access": "public", "registry": "https://registry.npmjs.org"}, "dependencies": {"@ui-tars/shared": "workspace:*", "@browserbasehq/stagehand": "^1.13.0", "big.js": "^6.2.2"}, "peerDependencies": {"@ui-tars/sdk": "workspace:*"}, "devDependencies": {"@ui-tars/sdk": "workspace:*", "@common/configs": "workspace:*", "@rslib/core": "^0.5.4", "typescript": "^5.7.2", "vitest": "^3.0.2", "@types/big.js": "^6.2.2"}}