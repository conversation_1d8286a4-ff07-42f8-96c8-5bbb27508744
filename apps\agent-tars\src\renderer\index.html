<!doctype html>
<html>
  <head>
    <meta charset="UTF-8" />
    <title>Agent TARS</title>
    <meta
      http-equiv="Content-Security-Policy"
      content="default-src 'self' 'unsafe-inline' file: data: blob:; img-src 'self' file: data: blob:; style-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com https://fonts.googleapis.com; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.jsdelivr.net/npm/ https://cdn.jsdelivr.net https://cdnjs.cloudflare.com; font-src 'self' https://cdnjs.cloudflare.com https://fonts.googleapis.com; worker-src 'self' blob:; connect-src 'self' https://cdn.jsdelivr.net/npm/ blob:"
    />
    <style>
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }

      .loading-spinner {
        display: inline-block;
        width: 30px;
        height: 30px;
        border: 3px solid rgba(0, 0, 0, 0.1);
        border-radius: 50%;
        border-top-color: #767676;
        animation: spin 1s ease-in-out infinite;
        margin: 20px;
      }

      .loading-container {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 100vh;
      }
    </style>
  </head>
  <body>
    <div id="root">
      <div class="loading-container">
        <div class="loading-spinner"></div>
      </div>
    </div>
    <script type="module" src="/src/main.tsx"></script>
    <!-- DATA -->
  </body>
</html>
