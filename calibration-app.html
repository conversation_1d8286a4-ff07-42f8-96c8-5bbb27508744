<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>UI-TARS 坐标校准工具</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            overflow: hidden;
            cursor: none;
            user-select: none;
        }

        .fullscreen-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            z-index: 9999;
            background: rgba(0, 0, 0, 0.95);
        }

        .calibration-point {
            position: absolute;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #ff4757;
            border: 4px solid #ffffff;
            box-shadow: 0 0 20px rgba(255, 71, 87, 0.8), 0 0 40px rgba(255, 71, 87, 0.4);
            cursor: pointer;
            transform: translate(-50%, -50%);
            animation: pulse 1.5s infinite;
            display: none;
            z-index: 10000;
        }

        .calibration-point.active {
            display: block;
            animation: pulse 1s infinite, glow 2s infinite;
        }

        .calibration-point.completed {
            background: #2ed573;
            box-shadow: 0 0 20px rgba(46, 213, 115, 0.8), 0 0 40px rgba(46, 213, 115, 0.4);
            animation: none;
        }

        @keyframes pulse {
            0% { transform: translate(-50%, -50%) scale(1); }
            50% { transform: translate(-50%, -50%) scale(1.2); }
            100% { transform: translate(-50%, -50%) scale(1); }
        }

        @keyframes glow {
            0%, 100% { box-shadow: 0 0 20px rgba(255, 71, 87, 0.8), 0 0 40px rgba(255, 71, 87, 0.4); }
            50% { box-shadow: 0 0 30px rgba(255, 71, 87, 1), 0 0 60px rgba(255, 71, 87, 0.6); }
        }

        .info-panel {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(255, 255, 255, 0.95);
            padding: 40px;
            border-radius: 20px;
            text-align: center;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
            backdrop-filter: blur(10px);
            z-index: 10001;
        }

        .info-panel h1 {
            color: #2c3e50;
            margin-bottom: 20px;
            font-size: 2.5em;
            font-weight: 300;
        }

        .info-panel p {
            color: #7f8c8d;
            margin-bottom: 15px;
            font-size: 1.2em;
            line-height: 1.6;
        }

        .start-button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 40px;
            border-radius: 50px;
            font-size: 1.2em;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-top: 20px;
        }

        .start-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
        }

        .progress-bar {
            position: fixed;
            top: 30px;
            left: 50%;
            transform: translateX(-50%);
            width: 300px;
            height: 8px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 4px;
            overflow: hidden;
            display: none;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2);
            width: 0%;
            transition: width 0.5s ease;
        }

        .status-text {
            position: fixed;
            top: 60px;
            left: 50%;
            transform: translateX(-50%);
            color: white;
            font-size: 1.5em;
            text-align: center;
            display: none;
        }

        .point-label {
            position: absolute;
            top: -60px;
            left: 50%;
            transform: translateX(-50%);
            color: white;
            font-size: 14px;
            font-weight: bold;
            background: rgba(0, 0, 0, 0.7);
            padding: 5px 10px;
            border-radius: 15px;
            white-space: nowrap;
        }

        .results-panel {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(255, 255, 255, 0.95);
            padding: 40px;
            border-radius: 20px;
            text-align: center;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
            backdrop-filter: blur(10px);
            z-index: 10001;
            display: none;
        }

        .close-button {
            background: #e74c3c;
            color: white;
            border: none;
            padding: 10px 30px;
            border-radius: 25px;
            font-size: 1em;
            cursor: pointer;
            margin-top: 20px;
            transition: all 0.3s ease;
        }

        .close-button:hover {
            background: #c0392b;
            transform: translateY(-1px);
        }
    </style>
</head>
<body>
    <div class="fullscreen-overlay">
        <!-- 信息面板 -->
        <div class="info-panel" id="infoPanel">
            <h1>🎯 UI-TARS 坐标校准</h1>
            <p>此工具将帮助校准您的本地模型的点击精度</p>
            <p>请让您的模型依次点击屏幕上出现的<strong>5个红色圆点</strong></p>
            <p>校准点位置：中央、上方、下方、左侧、右侧</p>
            <p>点击开始按钮启动校准程序</p>
            <button class="start-button" onclick="startCalibration()">开始校准</button>
        </div>

        <!-- 进度条 -->
        <div class="progress-bar" id="progressBar">
            <div class="progress-fill" id="progressFill"></div>
        </div>

        <!-- 状态文本 -->
        <div class="status-text" id="statusText">请点击红色圆点</div>

        <!-- 校准点 -->
        <div class="calibration-point" id="point1">
            <div class="point-label">中央点</div>
        </div>
        <div class="calibration-point" id="point2">
            <div class="point-label">上方点</div>
        </div>
        <div class="calibration-point" id="point3">
            <div class="point-label">下方点</div>
        </div>
        <div class="calibration-point" id="point4">
            <div class="point-label">左侧点</div>
        </div>
        <div class="calibration-point" id="point5">
            <div class="point-label">右侧点</div>
        </div>

        <!-- 结果面板 -->
        <div class="results-panel" id="resultsPanel">
            <h2>🎉 校准完成！</h2>
            <div id="resultsContent"></div>
            <button class="close-button" onclick="closeCalibration()">关闭校准工具</button>
        </div>
    </div>

    <script>
        let currentPointIndex = 0;
        let calibrationData = [];
        let targetPositions = [];

        const points = [
            { id: 'point1', name: '中央点' },
            { id: 'point2', name: '上方点' },
            { id: 'point3', name: '下方点' },
            { id: 'point4', name: '左侧点' },
            { id: 'point5', name: '右侧点' }
        ];

        function startCalibration() {
            document.getElementById('infoPanel').style.display = 'none';
            document.getElementById('progressBar').style.display = 'block';
            document.getElementById('statusText').style.display = 'block';
            
            setupCalibrationPoints();
            showNextPoint();
        }

        function setupCalibrationPoints() {
            const screenWidth = window.innerWidth;
            const screenHeight = window.innerHeight;
            const margin = 100; // 距离边缘的最小距离

            // 计算5个校准点的位置
            targetPositions = [
                { x: screenWidth / 2, y: screenHeight / 2 }, // 中央
                { x: screenWidth / 2, y: margin }, // 上方
                { x: screenWidth / 2, y: screenHeight - margin }, // 下方
                { x: margin, y: screenHeight / 2 }, // 左侧
                { x: screenWidth - margin, y: screenHeight / 2 } // 右侧
            ];

            // 设置每个点的位置
            points.forEach((point, index) => {
                const element = document.getElementById(point.id);
                element.style.left = targetPositions[index].x + 'px';
                element.style.top = targetPositions[index].y + 'px';
                
                // 添加点击事件
                element.onclick = () => handlePointClick(index, event);
            });
        }

        function showNextPoint() {
            if (currentPointIndex >= points.length) {
                completeCalibration();
                return;
            }

            // 隐藏所有点
            points.forEach(point => {
                document.getElementById(point.id).classList.remove('active');
            });

            // 显示当前点
            const currentPoint = document.getElementById(points[currentPointIndex].id);
            currentPoint.classList.add('active');

            // 更新状态
            document.getElementById('statusText').textContent = 
                `请点击 ${points[currentPointIndex].name} (${currentPointIndex + 1}/5)`;
            
            // 更新进度条
            const progress = (currentPointIndex / points.length) * 100;
            document.getElementById('progressFill').style.width = progress + '%';
        }

        function handlePointClick(pointIndex, event) {
            if (pointIndex !== currentPointIndex) return;

            // 记录点击位置
            const clickX = event.clientX;
            const clickY = event.clientY;
            const targetX = targetPositions[pointIndex].x;
            const targetY = targetPositions[pointIndex].y;

            // 计算偏差
            const offsetX = clickX - targetX;
            const offsetY = clickY - targetY;

            calibrationData.push({
                pointName: points[pointIndex].name,
                target: { x: targetX, y: targetY },
                actual: { x: clickX, y: clickY },
                offset: { x: offsetX, y: offsetY }
            });

            // 标记点为完成状态
            document.getElementById(points[pointIndex].id).classList.add('completed');
            document.getElementById(points[pointIndex].id).classList.remove('active');

            currentPointIndex++;
            
            setTimeout(() => {
                showNextPoint();
            }, 500);
        }

        function completeCalibration() {
            // 隐藏进度相关元素
            document.getElementById('progressBar').style.display = 'none';
            document.getElementById('statusText').style.display = 'none';

            // 计算平均偏差
            const avgOffsetX = calibrationData.reduce((sum, data) => sum + data.offset.x, 0) / calibrationData.length;
            const avgOffsetY = calibrationData.reduce((sum, data) => sum + data.offset.y, 0) / calibrationData.length;

            // 显示结果
            const resultsContent = document.getElementById('resultsContent');
            resultsContent.innerHTML = `
                <p><strong>平均偏差：</strong></p>
                <p>X轴偏差: ${avgOffsetX.toFixed(2)}px</p>
                <p>Y轴偏差: ${avgOffsetY.toFixed(2)}px</p>
                <p><strong>详细数据：</strong></p>
                ${calibrationData.map(data => `
                    <p>${data.pointName}: X偏差 ${data.offset.x.toFixed(1)}px, Y偏差 ${data.offset.y.toFixed(1)}px</p>
                `).join('')}
                <p style="margin-top: 20px; color: #27ae60;">
                    <strong>校准数据已保存到控制台，可用于模型坐标修正</strong>
                </p>
            `;

            // 输出到控制台供程序使用
            console.log('UI-TARS Calibration Results:', {
                averageOffset: { x: avgOffsetX, y: avgOffsetY },
                detailedData: calibrationData,
                timestamp: new Date().toISOString()
            });

            document.getElementById('resultsPanel').style.display = 'block';
        }

        function closeCalibration() {
            window.close();
        }

        // 防止右键菜单
        document.addEventListener('contextmenu', e => e.preventDefault());
        
        // 防止F11等快捷键
        document.addEventListener('keydown', e => {
            if (e.key === 'F11' || e.key === 'Escape') {
                e.preventDefault();
            }
        });

        // 自动全屏
        document.addEventListener('DOMContentLoaded', () => {
            if (document.documentElement.requestFullscreen) {
                document.documentElement.requestFullscreen();
            }
        });
    </script>
</body>
</html>
