<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>UI-TARS 坐标校准工具</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            overflow: hidden;
            user-select: none;
        }

        body.calibrating {
            /* 暂时不隐藏鼠标，方便调试 */
            /* cursor: none; */
        }

        .fullscreen-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            z-index: 9999;
            background: rgba(0, 0, 0, 0.95);
        }

        .calibration-point {
            position: absolute;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #ff4757;
            border: 4px solid #ffffff;
            box-shadow: 0 0 20px rgba(255, 71, 87, 0.8), 0 0 40px rgba(255, 71, 87, 0.4);
            cursor: pointer;
            transform: translate(-50%, -50%);
            animation: pulse 1.5s infinite;
            display: none;
            z-index: 10000;
        }

        .calibration-point.active {
            display: block;
            animation: pulse 1s infinite, glow 2s infinite;
        }

        .calibration-point.completed {
            background: #2ed573;
            box-shadow: 0 0 20px rgba(46, 213, 115, 0.8), 0 0 40px rgba(46, 213, 115, 0.4);
            animation: none;
        }

        @keyframes pulse {
            0% { transform: translate(-50%, -50%) scale(1); }
            50% { transform: translate(-50%, -50%) scale(1.2); }
            100% { transform: translate(-50%, -50%) scale(1); }
        }

        @keyframes glow {
            0%, 100% { box-shadow: 0 0 20px rgba(255, 71, 87, 0.8), 0 0 40px rgba(255, 71, 87, 0.4); }
            50% { box-shadow: 0 0 30px rgba(255, 71, 87, 1), 0 0 60px rgba(255, 71, 87, 0.6); }
        }

        .info-panel {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(255, 255, 255, 0.95);
            padding: 40px;
            border-radius: 20px;
            text-align: center;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
            backdrop-filter: blur(10px);
            z-index: 10001;
        }

        .info-panel h1 {
            color: #2c3e50;
            margin-bottom: 20px;
            font-size: 2.5em;
            font-weight: 300;
        }

        .info-panel p {
            color: #7f8c8d;
            margin-bottom: 15px;
            font-size: 1.2em;
            line-height: 1.6;
        }

        .start-button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 40px;
            border-radius: 50px;
            font-size: 1.2em;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-top: 20px;
        }

        .start-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
        }

        .progress-bar {
            position: fixed;
            top: 30px;
            left: 50%;
            transform: translateX(-50%);
            width: 300px;
            height: 8px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 4px;
            overflow: hidden;
            display: none;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2);
            width: 0%;
            transition: width 0.5s ease;
        }

        .status-text {
            position: fixed;
            top: 60px;
            left: 50%;
            transform: translateX(-50%);
            color: white;
            font-size: 1.5em;
            text-align: center;
            display: none;
        }

        .mouse-position {
            position: fixed;
            top: 10px;
            right: 10px;
            color: white;
            font-size: 14px;
            background: rgba(0, 0, 0, 0.7);
            padding: 5px 10px;
            border-radius: 5px;
            display: none;
            z-index: 10003;
        }

        .point-label {
            position: absolute;
            top: -60px;
            left: 50%;
            transform: translateX(-50%);
            color: white;
            font-size: 14px;
            font-weight: bold;
            background: rgba(0, 0, 0, 0.7);
            padding: 5px 10px;
            border-radius: 15px;
            white-space: nowrap;
        }

        .results-panel {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(255, 255, 255, 0.95);
            padding: 40px;
            border-radius: 20px;
            text-align: center;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
            backdrop-filter: blur(10px);
            z-index: 10001;
            display: none;
        }

        .close-button {
            background: #e74c3c;
            color: white;
            border: none;
            padding: 10px 30px;
            border-radius: 25px;
            font-size: 1em;
            cursor: pointer;
            margin-top: 20px;
            transition: all 0.3s ease;
        }

        .close-button:hover {
            background: #c0392b;
            transform: translateY(-1px);
        }

        .click-feedback {
            position: absolute;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            background: #f39c12;
            border: 4px solid #ffffff;
            transform: translate(-50%, -50%);
            animation: clickFade 3s ease-out forwards;
            z-index: 10002;
            pointer-events: none;
            box-shadow: 0 0 20px rgba(243, 156, 18, 0.8);
        }

        @keyframes clickFade {
            0% {
                opacity: 1;
                transform: translate(-50%, -50%) scale(1);
            }
            100% {
                opacity: 0;
                transform: translate(-50%, -50%) scale(2);
            }
        }
    </style>
</head>
<body>
    <div class="fullscreen-overlay">
        <!-- 信息面板 -->
        <div class="info-panel" id="infoPanel">
            <h1>🎯 UI-TARS 坐标校准</h1>
            <p>此工具将帮助校准您的本地模型的点击精度</p>
            <p>请让您的模型依次点击屏幕上出现的<strong>5个红色圆点</strong></p>
            <p>校准点位置：中央、上方、下方、左侧、右侧</p>
            <p>模型点击任何位置都会被记录，无需精确命中目标</p>
            <p style="color: #27ae60;"><strong>全屏模式：</strong>推荐使用全屏获得最佳校准效果</p>
            <p style="color: #e74c3c;"><strong>如果虚拟鼠标无法检测：</strong></p>
            <p style="color: #e74c3c;">• 按K键开启键盘模式，然后按空格键模拟点击</p>
            <p style="color: #e74c3c;">• 或者手动协助模型完成校准</p>
            <p style="color: #7f8c8d;">快捷键：F11切换全屏 | ESC重置校准 | K键盘模式</p>
            <button class="start-button" onclick="enterFullscreenAndStart()">进入全屏并开始校准</button>
            <button class="start-button" onclick="startCalibration()" style="background: #95a5a6; margin-top: 10px;">不全屏直接开始</button>
        </div>

        <!-- 进度条 -->
        <div class="progress-bar" id="progressBar">
            <div class="progress-fill" id="progressFill"></div>
        </div>

        <!-- 状态文本 -->
        <div class="status-text" id="statusText">请点击红色圆点</div>

        <!-- 鼠标位置显示 -->
        <div class="mouse-position" id="mousePosition">鼠标位置: (0, 0)</div>

        <!-- 校准点 -->
        <div class="calibration-point" id="point1">
            <div class="point-label">中央点</div>
        </div>
        <div class="calibration-point" id="point2">
            <div class="point-label">上方点</div>
        </div>
        <div class="calibration-point" id="point3">
            <div class="point-label">下方点</div>
        </div>
        <div class="calibration-point" id="point4">
            <div class="point-label">左侧点</div>
        </div>
        <div class="calibration-point" id="point5">
            <div class="point-label">右侧点</div>
        </div>

        <!-- 结果面板 -->
        <div class="results-panel" id="resultsPanel">
            <h2>🎉 校准完成！</h2>
            <div id="resultsContent"></div>
            <button class="close-button" onclick="resetCalibration()" style="background: #3498db; margin-right: 10px;">重新校准</button>
            <button class="close-button" onclick="closeCalibration()">关闭校准工具</button>
        </div>
    </div>

    <script>
        let currentPointIndex = 0;
        let calibrationData = [];
        let targetPositions = [];
        let calibrationStarted = false;
        let keyboardMode = false;

        const points = [
            { id: 'point1', name: '中央点' },
            { id: 'point2', name: '上方点' },
            { id: 'point3', name: '下方点' },
            { id: 'point4', name: '左侧点' },
            { id: 'point5', name: '右侧点' }
        ];

        function enterFullscreenAndStart() {
            // 尝试进入全屏
            const element = document.documentElement;

            if (element.requestFullscreen) {
                element.requestFullscreen().then(() => {
                    setTimeout(startCalibration, 500);
                }).catch(err => {
                    console.log('全屏失败:', err);
                    alert('无法进入全屏模式，将在窗口模式下开始校准');
                    startCalibration();
                });
            } else if (element.webkitRequestFullscreen) {
                element.webkitRequestFullscreen();
                setTimeout(startCalibration, 500);
            } else if (element.msRequestFullscreen) {
                element.msRequestFullscreen();
                setTimeout(startCalibration, 500);
            } else if (element.mozRequestFullScreen) {
                element.mozRequestFullScreen();
                setTimeout(startCalibration, 500);
            } else {
                alert('您的浏览器不支持全屏模式，将在窗口模式下开始校准');
                startCalibration();
            }
        }

        function startCalibration() {
            document.getElementById('infoPanel').style.display = 'none';
            document.getElementById('progressBar').style.display = 'block';
            document.getElementById('statusText').style.display = 'block';
            document.getElementById('mousePosition').style.display = 'block';

            // 暂时不隐藏鼠标光标，方便调试
            document.body.classList.add('calibrating');

            // 显示倒计时
            let countdown = 3;
            const statusText = document.getElementById('statusText');
            statusText.textContent = `校准将在 ${countdown} 秒后开始...`;

            const countdownInterval = setInterval(() => {
                countdown--;
                if (countdown > 0) {
                    statusText.textContent = `校准将在 ${countdown} 秒后开始...`;
                } else {
                    clearInterval(countdownInterval);
                    statusText.textContent = '校准开始！';

                    // 再延迟500ms开始真正的校准
                    setTimeout(() => {
                        calibrationStarted = true;
                        setupCalibrationPoints();
                        showNextPoint();
                    }, 500);
                }
            }, 1000);
        }

        function setupCalibrationPoints() {
            const screenWidth = window.innerWidth;
            const screenHeight = window.innerHeight;
            const margin = 100; // 距离边缘的最小距离

            // 计算5个校准点的位置
            targetPositions = [
                { x: screenWidth / 2, y: screenHeight / 2 }, // 中央
                { x: screenWidth / 2, y: margin }, // 上方
                { x: screenWidth / 2, y: screenHeight - margin }, // 下方
                { x: margin, y: screenHeight / 2 }, // 左侧
                { x: screenWidth - margin, y: screenHeight / 2 } // 右侧
            ];

            // 设置每个点的位置
            points.forEach((point, index) => {
                const element = document.getElementById(point.id);
                element.style.left = targetPositions[index].x + 'px';
                element.style.top = targetPositions[index].y + 'px';

                // 添加点击事件
                element.onclick = () => handlePointClick(index, event);
            });

            // 添加多种事件监听，捕获虚拟鼠标点击
            document.addEventListener('click', handleAnyClick);
            document.addEventListener('mousedown', handleAnyClick);
            document.addEventListener('mouseup', handleAnyClick);
            document.addEventListener('pointerdown', handleAnyClick);
            document.addEventListener('pointerup', handleAnyClick);

            // 添加触摸事件支持
            document.addEventListener('touchstart', handleTouchEvent);
            document.addEventListener('touchend', handleTouchEvent);

            // 添加鼠标移动跟踪
            document.addEventListener('mousemove', trackMousePosition);
        }

        function showNextPoint() {
            if (currentPointIndex >= points.length) {
                completeCalibration();
                return;
            }

            // 隐藏所有点
            points.forEach(point => {
                document.getElementById(point.id).classList.remove('active');
            });

            // 显示当前点
            const currentPoint = document.getElementById(points[currentPointIndex].id);
            currentPoint.classList.add('active');

            // 更新状态
            document.getElementById('statusText').textContent =
                `请让模型尝试点击 ${points[currentPointIndex].name} (${currentPointIndex + 1}/5) - 任何位置的点击都会被记录`;
            
            // 更新进度条
            const progress = (currentPointIndex / points.length) * 100;
            document.getElementById('progressFill').style.width = progress + '%';
        }

        function handleTouchEvent(event) {
            if (!calibrationStarted) return;
            if (currentPointIndex >= points.length || currentPointIndex < 0) return;

            event.preventDefault();
            event.stopPropagation();

            const touch = event.touches[0] || event.changedTouches[0];
            if (touch) {
                console.log('触摸事件检测到:', touch.clientX, touch.clientY);
                processClick(touch.clientX, touch.clientY, 'touch');
            }
        }

        function handleAnyClick(event) {
            // 只在校准真正开始后响应点击
            if (!calibrationStarted) return;

            // 只在校准进行中响应点击
            if (currentPointIndex >= points.length || currentPointIndex < 0) return;

            // 防止重复处理同一个点击
            if (event.type === 'mouseup' && event.detail === 0) {
                // 这可能是虚拟鼠标点击
                console.log('虚拟鼠标点击检测到:', event.clientX, event.clientY);
            } else if (event.type === 'click') {
                console.log('普通点击检测到:', event.clientX, event.clientY);
            } else if (event.type === 'mousedown') {
                console.log('鼠标按下检测到:', event.clientX, event.clientY);
            } else if (event.type === 'pointerdown' || event.type === 'pointerup') {
                console.log('指针事件检测到:', event.clientX, event.clientY);
            }

            // 防止事件冒泡
            event.stopPropagation();

            processClick(event.clientX, event.clientY, event.type);
        }

        function processClick(x, y, eventType) {
            // 防止重复处理
            const now = Date.now();
            if (window.lastClickTime && now - window.lastClickTime < 100) {
                return; // 100ms内的重复点击忽略
            }
            window.lastClickTime = now;

            const targetX = targetPositions[currentPointIndex].x;
            const targetY = targetPositions[currentPointIndex].y;

            // 计算偏差
            const offsetX = x - targetX;
            const offsetY = y - targetY;

            calibrationData.push({
                pointName: points[currentPointIndex].name,
                target: { x: targetX, y: targetY },
                actual: { x: x, y: y },
                offset: { x: offsetX, y: offsetY },
                eventType: eventType
            });

            console.log(`校准点 ${currentPointIndex + 1} 完成:`, {
                target: { x: targetX, y: targetY },
                actual: { x: x, y: y },
                offset: { x: offsetX, y: offsetY },
                eventType: eventType
            });

            // 标记点为完成状态
            document.getElementById(points[currentPointIndex].id).classList.add('completed');
            document.getElementById(points[currentPointIndex].id).classList.remove('active');

            // 显示点击位置（可选，用于调试）
            showClickFeedback(x, y);

            currentPointIndex++;

            setTimeout(() => {
                showNextPoint();
            }, 800);
        }

        function trackMousePosition(event) {
            const mousePos = document.getElementById('mousePosition');
            if (mousePos && calibrationStarted) {
                mousePos.style.display = 'block';
                mousePos.textContent = `鼠标位置: (${event.clientX}, ${event.clientY})`;
            }
        }

        function showClickFeedback(x, y) {
            // 创建点击反馈元素
            const feedback = document.createElement('div');
            feedback.className = 'click-feedback';
            feedback.style.left = x + 'px';
            feedback.style.top = y + 'px';

            // 添加点击位置文本
            const label = document.createElement('div');
            label.style.position = 'absolute';
            label.style.top = '40px';
            label.style.left = '50%';
            label.style.transform = 'translateX(-50%)';
            label.style.color = 'white';
            label.style.fontSize = '12px';
            label.style.background = 'rgba(0,0,0,0.8)';
            label.style.padding = '2px 6px';
            label.style.borderRadius = '3px';
            label.style.whiteSpace = 'nowrap';
            label.textContent = `点击: (${Math.round(x)}, ${Math.round(y)})`;
            feedback.appendChild(label);

            document.body.appendChild(feedback);

            // 3秒后移除元素
            setTimeout(() => {
                if (feedback.parentNode) {
                    feedback.parentNode.removeChild(feedback);
                }
            }, 3000);
        }

        function handlePointClick(pointIndex, event) {
            // 这个函数现在主要用于直接点击校准点的情况
            event.stopPropagation();
            handleAnyClick(event);
        }

        function completeCalibration() {
            // 隐藏进度相关元素
            document.getElementById('progressBar').style.display = 'none';
            document.getElementById('statusText').style.display = 'none';

            // 计算平均偏差
            const avgOffsetX = calibrationData.reduce((sum, data) => sum + data.offset.x, 0) / calibrationData.length;
            const avgOffsetY = calibrationData.reduce((sum, data) => sum + data.offset.y, 0) / calibrationData.length;

            // 显示结果
            const resultsContent = document.getElementById('resultsContent');
            resultsContent.innerHTML = `
                <p><strong>平均偏差：</strong></p>
                <p>X轴偏差: ${avgOffsetX.toFixed(2)}px</p>
                <p>Y轴偏差: ${avgOffsetY.toFixed(2)}px</p>
                <p><strong>详细数据：</strong></p>
                ${calibrationData.map(data => `
                    <p>${data.pointName}: X偏差 ${data.offset.x.toFixed(1)}px, Y偏差 ${data.offset.y.toFixed(1)}px</p>
                `).join('')}
                <p style="margin-top: 20px; color: #27ae60;">
                    <strong>校准数据已保存到控制台，可用于模型坐标修正</strong>
                </p>
            `;

            // 输出到控制台供程序使用
            console.log('UI-TARS Calibration Results:', {
                averageOffset: { x: avgOffsetX, y: avgOffsetY },
                detailedData: calibrationData,
                timestamp: new Date().toISOString()
            });

            document.getElementById('resultsPanel').style.display = 'block';
        }

        function resetCalibration() {
            // 重置所有状态
            currentPointIndex = 0;
            calibrationData = [];
            calibrationStarted = false;

            // 恢复鼠标光标
            document.body.classList.remove('calibrating');

            // 退出全屏
            exitFullscreen();

            // 移除所有事件监听器
            document.removeEventListener('click', handleAnyClick);
            document.removeEventListener('mousedown', handleAnyClick);
            document.removeEventListener('mouseup', handleAnyClick);
            document.removeEventListener('pointerdown', handleAnyClick);
            document.removeEventListener('pointerup', handleAnyClick);
            document.removeEventListener('touchstart', handleTouchEvent);
            document.removeEventListener('touchend', handleTouchEvent);

            // 隐藏所有校准相关元素
            document.getElementById('progressBar').style.display = 'none';
            document.getElementById('statusText').style.display = 'none';
            document.getElementById('resultsPanel').style.display = 'none';
            document.getElementById('mousePosition').style.display = 'none';

            // 重置所有点的状态
            points.forEach(point => {
                const element = document.getElementById(point.id);
                element.classList.remove('active', 'completed');
            });

            // 清除所有点击反馈元素
            const feedbacks = document.querySelectorAll('.click-feedback');
            feedbacks.forEach(feedback => feedback.remove());

            // 显示初始面板
            document.getElementById('infoPanel').style.display = 'block';
        }

        function exitFullscreen() {
            if (document.exitFullscreen) {
                document.exitFullscreen();
            } else if (document.webkitExitFullscreen) {
                document.webkitExitFullscreen();
            } else if (document.msExitFullscreen) {
                document.msExitFullscreen();
            } else if (document.mozCancelFullScreen) {
                document.mozCancelFullScreen();
            }
        }

        function closeCalibration() {
            // 恢复鼠标光标
            document.body.classList.remove('calibrating');
            // 退出全屏
            exitFullscreen();
            // 延迟关闭窗口，让全屏退出完成
            setTimeout(() => {
                window.close();
            }, 500);
        }

        // 防止右键菜单
        document.addEventListener('contextmenu', e => e.preventDefault());
        
        // 快捷键处理
        document.addEventListener('keydown', e => {
            if (e.key === 'F11') {
                e.preventDefault();
                // F11切换全屏
                if (document.fullscreenElement) {
                    exitFullscreen();
                } else {
                    // 如果校准还没开始，则进入全屏并开始校准
                    // 如果已经开始，则只进入全屏
                    const element = document.documentElement;
                    if (element.requestFullscreen) {
                        element.requestFullscreen().catch(err => {
                            console.log('全屏失败:', err);
                        });
                    }
                }
            }
            if (e.key === 'Escape') {
                // ESC键重置校准
                resetCalibration();
            }
            if (e.key === 'Space' && calibrationStarted && currentPointIndex < points.length) {
                // 空格键模拟点击（备选方案）
                e.preventDefault();
                const target = targetPositions[currentPointIndex];
                // 模拟在目标位置附近的随机点击
                const randomX = target.x + (Math.random() - 0.5) * 100;
                const randomY = target.y + (Math.random() - 0.5) * 100;
                console.log('键盘模拟点击:', randomX, randomY);
                processClick(randomX, randomY, 'keyboard');
            }
            if (e.key === 'k' || e.key === 'K') {
                // K键切换键盘模式
                keyboardMode = !keyboardMode;
                console.log('键盘模式:', keyboardMode ? '开启' : '关闭');
                if (keyboardMode) {
                    document.getElementById('statusText').textContent += ' (按空格键模拟点击)';
                }
            }
        });

        // 手动全屏提示
        document.addEventListener('DOMContentLoaded', () => {
            // 不自动全屏，避免触发意外事件
            console.log('校准应用已加载，请手动按F11进入全屏模式以获得最佳效果');
        });
    </script>
</body>
</html>
