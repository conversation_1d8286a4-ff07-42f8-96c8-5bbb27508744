## 0.4.0 2024-12-10 - add logging

## 1.1.1

### Patch Changes

- a40b3c1: feat: add mcp http server
- 82fe970: fix: hang up
- feat: support high performance sse and http server
- Updated dependencies [a40b3c1]
- Updated dependencies [82fe970]
- Updated dependencies
  - mcp-http-server@1.1.1

## 1.1.1-beta.3

### Patch Changes

- fix: hang up
- Updated dependencies
  - mcp-http-server@1.1.1-beta.3

## 1.1.1-beta.2

### Patch Changes

- feat: add mcp http server
- Updated dependencies
  - @agent-infra/mcp-http-server@1.1.1-beta.2

## 1.1.1-beta.1

## 1.1.1-beta.0

## 1.1.0

### Minor Changes

- feat(agent-tars): provider mcp servers

### Patch Changes

- 9089c63: fix: version
- 4860d9e: feat: new version release
- 9089c63: refactor: mcp servers with high-level api

## 1.0.1-beta.15

### Patch Changes

- feat: new version release

## 1.0.1-beta.14

## 1.0.1-beta.13

## 1.0.1-beta.12

## 1.0.1-beta.11

## 1.0.1-beta.10

## 1.0.1-beta.9

## 1.0.1-beta.8

### Patch Changes

- fix: version

## 1.0.1-beta.7

### Patch Changes

- refactor: mcp servers with high-level api

- See Logging in [README.md](./README.md#logging)
- By default, errors will now be logged
- Use the new `--verbose` flag for detailed logging
