import {
  Hand,
  Keyboard,
  Type,
  MousePointerClick,
  ScrollText,
  AlertCircle,
  CheckSquare,
  RotateCcw,
  Hourglass,
  Camera,
} from 'lucide-react';

export const ActionIconMap = {
  scroll: ScrollText,
  drag: Hand,
  hotkey: Keyboard,
  type: Type,
  click: <PERSON><PERSON>ointer<PERSON>lick,
  left_double: <PERSON><PERSON><PERSON>er<PERSON>lick,
  right_single: <PERSON><PERSON>ointer<PERSON>lick,
  error_env: AlertCircle,
  finished: CheckSquare,
  call_user: RotateCcw,
  wait: Hourglass,
  screenshot: Camera,
};
