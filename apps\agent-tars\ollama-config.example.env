# Ollama Configuration for Agent TARS
# Copy this to your .env file or set these environment variables

# Ollama API Configuration
OLLAMA_API_KEY=ollama
OLLAMA_BASE_URL=http://localhost:11434/v1
OLLAMA_DEFAULT_MODEL=qwen2.5vl:3b

# Alternative models you can use with Ollama:
# OLLAMA_DEFAULT_MODEL=qwen2.5vl:7b
# OLLAMA_DEFAULT_MODEL=qwen2.5vl:32b
# OLLAMA_DEFAULT_MODEL=llama3.2-vision:11b
# OLLAMA_DEFAULT_MODEL=llama3.2-vision:90b
# OLLAMA_DEFAULT_MODEL=minicpm-v:8b

# Instructions:
# 1. Make sure Ollama is running on your system
# 2. Download your desired model: ollama pull qwen2.5vl:3b
# 3. Start Ollama: ollama serve (if not running as service)
# 4. Set these environment variables or copy to .env file
# 5. In the app settings, select "<PERSON><PERSON><PERSON>" as provider and your model name 