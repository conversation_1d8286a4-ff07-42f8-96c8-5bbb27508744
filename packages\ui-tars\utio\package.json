{"name": "@ui-tars/utio", "version": "1.2.1", "description": "UTIO (UI-TARS Insights and Observation)", "main": "./dist/index.js", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "scripts": {"prepare": "npm run build", "dev": "rslib build --watch", "build": "rslib build", "build:watch": "rslib build --watch"}, "keywords": ["UI-TARS"], "license": "Apache-2.0", "publishConfig": {"registry": "https://registry.npmjs.org/"}, "files": ["dist", "src"], "dependencies": {}, "devDependencies": {"@common/configs": "workspace:*", "@rslib/core": "^0.5.4", "typescript": "^5.7.2"}}