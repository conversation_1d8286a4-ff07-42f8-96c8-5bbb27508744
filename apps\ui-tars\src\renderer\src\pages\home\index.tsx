/**
 * Copyright (c) 2025 Bytedance, Inc. and its affiliates.
 * SPDX-License-Identifier: Apache-2.0
 */
import React, { useState } from 'react';
import RunMessages from '@renderer/components/RunMessages';

import { AppSidebar } from '@/renderer/src/components/SideBar/app-sidebar';
import { SidebarInset, SidebarProvider } from '@renderer/components/ui/sidebar';
import { DragArea } from '@renderer/components/Common/drag';
import { CalibrationOverlay } from '@renderer/components/CalibrationOverlay';

export default function Page() {
  const [showCalibration, setShowCalibration] = useState(false);

  const handleCalibrationComplete = (calibrationData: any) => {
    console.log('Calibration completed:', calibrationData);
    // 可以在这里显示校准完成的通知
  };

  return (
    <SidebarProvider className="flex h-screen w-full bg-white">
      <AppSidebar onCalibrationClick={() => setShowCalibration(true)} />
      <SidebarInset className="flex-1">
        <DragArea />
        <RunMessages />
      </SidebarInset>

      <CalibrationOverlay
        isVisible={showCalibration}
        onClose={() => setShowCalibration(false)}
        onCalibrationComplete={handleCalibrationComplete}
      />
    </SidebarProvider>
  );
}
